

<?php

//error_reporting(0);
require_once 'swift_required.php';
date_default_timezone_set('Asia/Kuala_Lumpur');
header('Content-Type: application/json;charset=utf-8'); //json header
ini_set('max_execution_time', 60);

include '../db/connection.php';
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	
	// Retrieve email owner test gear
	// Main datatable

		$sql = "SELECT MME_ASSET.Asset, MME_ASSET.Cost_Center, MME_ASSET.Inventory_Number, MME_ASSET.Asset_Description,MME_ASSET.Asset_Description_editable,
				MME_ASSET.Tagging_Date, MME_ASSET.Serial_Number, MME_ASSET.Verification_By, USERS.staff_id, USERS.fullname,MME_ASSET.Health_Check_Date,MME_ASSET.status,MME_ASSET.Verif_Calib_Date, MME_ASSET.region, USERS.email 
				FROM MME_ASSET,USERS
				WHERE MME_ASSET.Verification_By = USERS.staff_id AND MME_ASSET.Verification_By != '' AND MME_ASSET.Health_Check_Date != '1900-01-01' AND USERS.email != ''  AND MME_ASSET.status != 'FAULTY' ";
					
		$stmt = sqlsrv_query( $conn, $sql );
			
		if( $stmt === false) {
			die( print_r( sqlsrv_errors(), true) );		
		}
		
		if(sqlsrv_has_rows($stmt) != 1){ 
		  // echo "Invalid Username and Password.";
		}
		else {
			$i = 1;
			while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
					$asset                = htmlspecialchars($row["Asset"]);
					$cost_center          = htmlspecialchars($row["Cost_Center"]);
					$inventory_name       = htmlspecialchars($row["Asset_Description"])." ".htmlspecialchars($row["Asset_Description_editable"]);
					$serial_number        = htmlspecialchars($row["Serial_Number"]);
					$health_check_date    = date_format($row["Health_Check_Date"],'Y-m-d');
					$verif_calib_date     = date_format($row["Verif_Calib_Date"],'Y-m-d');
					$verification_by      = htmlspecialchars($row["Verification_By"]);
					$status               = htmlspecialchars($row["status"]);
					$staff_id             = htmlspecialchars($row["staff_id"]);
					$fullname             = htmlspecialchars($row["fullname"]);			
					$email                = htmlspecialchars($row["email"]);	
					$email = filter_var($row["email"],FILTER_SANITIZE_EMAIL);

					//$email = "<EMAIL>";
					
					
					if($email != ""){
						
						$today_date = date("Y-m-d");
						if(($today_date >= $health_check_date) && ($status == 'AVAILABLE')){
							echo $email.'<br>';
							echo $asset;
							
							$test = [];
							array_push($test, $email);
							

							
							//Create the Transport
							/* $transport = Swift_SmtpTransport::newInstance('***********', 25)
							;

							//Create the Mailer using your created Transport
							$mailer = Swift_Mailer::newInstance($transport);
							

							//Create a message
							$message = Swift_Message::newInstance('<just bg nama instance>')
							->setSubject('e-COMME : Health Check Activity Reminder For Test Gear - '.$asset.' - '.$inventory_name )
							->setFrom(array('<EMAIL>' => 'e-COMME'))
							->setTo(array($email))
							->setBody(
							'<html>' .
							'<head></head>' .
							'<body>' .
							'Dear '.$fullname.',<br><br>
							Your Item '.$asset.' - '.$inventory_name.' is already due for Health Check Activity ( '.$health_check_date.' ).
							Kindly log on to COMME website for further details and complete such action.'.'<br>			
							e-COMME : https://ifme.tm.com.my/eCOMME/  </br>'.									
							'<br>Login using GEMS Username and Password'.
							'<br><br>Thank you.'.
							'</body>' .
							'</html>',
							'text/html' // Mark the content-type as HTML
							)
							;
							//Send the message
							//echo $asset.$email;
							$result = $mailer->send($message); */
						}
						/* $one_day_before = date('Y-m-d', strtotime($health_check_date. " - 1 days"));
					
						// Reminder 1 days before for Health Check Activity.
						if($today_date >= $one_day_before){
							if(($today_date < $health_check_date) && ($status == 'AVAILABLE')){
								//Create the Transport								
								//$transport = Swift_SmtpTransport::newInstance('***********', 25)
								$transport = Swift_SmtpTransport::newInstance('webmail2014.tm.com.my', 25)
								//->setUsername('ipgen')
								//->setPassword('ipgen001')   //if fail to send email, please update password into IDM
								;

								//Create the Mailer using your created Transport
								 $mailer = Swift_Mailer::newInstance($transport);
							

								//Create a message
								$message = Swift_Message::newInstance('<just bg nama instance>')
								->setSubject('e-COMME : Health Check Due Date Reminder - '.$asset.' - '.$inventory_name)
								->setFrom(array('<EMAIL>' => 'e-COMME'))
								->setTo(array($email))
								->setBody(
								'<html>' .
								'<head></head>' .
								'<body>' .
								'Dear '.$fullname.',<br><br>
								Your Item '.$asset.' - '.$inventory_name.' is already approaching due date for Health Check Activity ( '.$health_check_date.' ).
								Kindly log on to COMME website for further details and complete such action.'.'<br>			
								e-COMME : https://ifme.tm.com.my/eCOMME/ </br>'.								
								'<br>Login using GEMS Username and Password'.
								'<br><br>Thank you.'.
								'</body>' .
								'</html>',
								'text/html' // Mark the content-type as HTML
								)
								;
								//Send the message
								$result = $mailer->send($message);
							}						
						}

					
						//Reminder 3 months Before for Verification = monthly
						$three_months_before = date('Y-m-d', strtotime($verif_calib_date. "-3 Months"));
						$two_months_before = date('Y-m-d', strtotime($verif_calib_date. "-2 Months"));
						$one_months_before = date('Y-m-d', strtotime($verif_calib_date. "-1 Months"));
						
						if(($today_date == $three_months_before) || ($today_date == $two_months_before) || ($today_date == $one_months_before) ){
							if($today_date >= $three_months_before){
								if($today_date < $verif_calib_date){								
									//Create the Transport
									$transport = Swift_SmtpTransport::newInstance('***********', 25)
									//$transport = Swift_SmtpTransport::newInstance('webmail2014.tm.com.my', 25)
									//->setUsername('ipgen')
									//->setPassword('ipgen001')   //if fail to send email, please update password into IDM
									;

									//Create the Mailer using your created Transport
									$mailer = Swift_Mailer::newInstance($transport);
								

									//Create a message
									$message = Swift_Message::newInstance('<just bg nama instance>')
									->setSubject('e-COMME : Verification / Calibration Due Date Reminder - '.$asset.' - '.$inventory_name)
									->setFrom(array('<EMAIL>' => 'e-COMME'))
									->setTo(array($email))
									->setBody(
									'<html>' .
									'<head></head>' .
									'<body>' .
									'Dear '.$fullname.',<br><br>
									Your Item '.$asset.' - '.$inventory_name.' is already approaching due date for Due Date Calibration Activity ( '.$verif_calib_date.' ).
									Kindly log on to COMME website for further details and complete such action.'.'<br>			
									e-COMME : https://ifme.tm.com.my/eCOMME/  </br>'.									
									'<br>Login using GEMS Username and Password'.
									'<br><br>Thank you.'.
									'</body>' .
									'</html>',
									'text/html' // Mark the content-type as HTML
									)
									;
									//Send the message
									$result = $mailer->send($message);
								}
							}
						}

 */
						//REMOVE status if want to check every single week. this one doesnt check if it in used.
						// health check already due date
						/* if(($today_date >= $health_check_date) && ($status == 'AVAILABLE')){
							
							//Create the Transport
							$transport = Swift_SmtpTransport::newInstance('***********', 25)
							//$transport = Swift_SmtpTransport::newInstance('webmail2014.tm.com.my', 25)
							//->setUsername('ipgen')
							//->setPassword('ipgen001')   //if fail to send email, please update password into IDM
							;

							//Create the Mailer using your created Transport
							$mailer = Swift_Mailer::newInstance($transport);
							

							//Create a message
							$message = Swift_Message::newInstance('<just bg nama instance>')
							->setSubject('e-COMME : Health Check Activity Reminder For Test Gear - '.$asset.' - '.$inventory_name )
							->setFrom(array('<EMAIL>' => 'e-COMME'))
							->setTo(array($email))
							->setBody(
							'<html>' .
							'<head></head>' .
							'<body>' .
							'Dear '.$fullname.',<br><br>
							Your Item '.$asset.' - '.$inventory_name.' is already due for Health Check Activity ( '.$health_check_date.' ).
							Kindly log on to COMME website for further details and complete such action.'.'<br>			
							e-COMME : https://ifme.tm.com.my/eCOMME/  </br>'.									
							'<br>Login using GEMS Username and Password'.
							'<br><br>Thank you.'.
							'</body>' .
							'</html>',
							'text/html' // Mark the content-type as HTML
							)
							;
							//Send the message
							//echo $asset.$email;
							$result = $mailer->send($message);
							
							
						}
						//verification
						if($today_date >= $verif_calib_date){
							
							//Create the Transport
							$transport = Swift_SmtpTransport::newInstance('***********', 25)
							//$transport = Swift_SmtpTransport::newInstance('webmail2014.tm.com.my', 25)
							//->setUsername('ipgen')
							//->setPassword('ipgen001')   //if fail to send email, please update password into IDM
							;

							//Create the Mailer using your created Transport
							$mailer = Swift_Mailer::newInstance($transport);
							

							//Create a message
							$message = Swift_Message::newInstance('<just bg nama instance>')
							->setSubject('e-COMME : Verification/Calibration Activity Reminder - '.$asset.' - '.$inventory_name)
							->setFrom(array('<EMAIL>' => 'e-COMME'))
							->setTo(array($email))
							->setBody(
							'<html>' .
							'<head></head>' .
							'<body>' .
							'Dear '.$fullname.',<br><br>
							Your Item '.$asset.' - '.$inventory_name.' is already due for Verification/ Calibration Activity ( '.$verif_calib_date.' ).
							Kindly log on to COMME website for further details and complete such action.'.'<br>			
							e-COMME : https://ifme.tm.com.my/eCOMME/  </br>'.								
							'<br>Login using GEMS Username and Password'.
							'<br><br>Thank you.'.
							'</body>' .
							'</html>',
							'text/html' // Mark the content-type as HTML
							)
							;
							//Send the message
							$result = $mailer->send($message);			
							
						}  
						
							 */
					}

 
					
			}		
			//echo $result = json_encode(array('data' => 'SUCCESS'));
			echo "SUCCESS";
			print_r($test);
		}	
		
		
}
else{
		echo "Connection could not be established.<br />";
		die( print_r( sqlsrv_errors(), true));
	}

?>

