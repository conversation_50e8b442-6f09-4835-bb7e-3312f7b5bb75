
<?php

//error_reporting(0);
require_once 'swift_required.php';
date_default_timezone_set('Asia/Kuala_Lumpur');
header('Content-Type: application/json;charset=utf-8'); //json header

//include '../db/connection.php';
$serverName = "172.20.49.37"; //serverName\instanceName
$connectionInfo = array( "Database"=>"COMME", "UID"=>"comme", "PWD"=>"%user456");
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	
//master calibration reminder
			
			$sql2 = "SELECT MME_MASTER_ASSET2.serial_no, MME_MASTER_ASSET2.master_mme_name,MME_MASTER_ASSET2.next_calib_date, USERS.staff_id, USERS.fullname,USERS.email, MME_MASTER_ASSET2.remarks
					FROM MME_MASTER_ASSET2,USERS
					WHERE MME_MASTER_ASSET2.staff_id = USERS.staff_id AND MME_MASTER_ASSET2.staff_id != '' AND MME_MASTER_ASSET2.next_calib_date != '1900-01-01' AND MME_MASTER_ASSET2.remarks = 'READY IN VC'";
					 
				
			//Reminder MASTER ASEET
			$sql2 = "SELECT MME_MASTER_ASSET2.serial_no, MME_MASTER_ASSET2.master_mme_name,MME_MASTER_ASSET2.calibration_due_date, MME_MASTER_ASSET2.health_check_date, USERS.staff_id, USERS.fullname,USERS.email, MME_MASTER_ASSET2.status
			FROM MME_MASTER_ASSET2,USERS
			WHERE MME_MASTER_ASSET2.staff_id = USERS.staff_id AND MME_MASTER_ASSET2.staff_id != '' AND MME_MASTER_ASSET2.calibration_due_date != '1900-01-01' ";
			
						
			$stmt2 = sqlsrv_query( $conn, $sql2 );
				
			if( $stmt2 === false) {
				die( print_r( sqlsrv_errors(), true) );		
			}
			
			if(sqlsrv_has_rows($stmt2) != 1){ 
			  // echo "Invalid Username and Password.";
			}
			else {
				while( $row = sqlsrv_fetch_array( $stmt2, SQLSRV_FETCH_ASSOC) ) {
					$serial_no                = trim($row["serial_no"]);
					$master_mme_name          = trim($row["master_mme_name"]);
					$calibration_due_date     = date_format($row["calibration_due_date"],'Y-m-d');
					$health_check_date        = date_format($row["health_check_date"],'Y-m-d');
					$staff_id                 = trim($row["staff_id"]);
					$fullname                 = trim($row["fullname"]);			
					$email                    = trim($row["email"]);	
					$status                   = trim($row["status"]);	
					
					//email = '<EMAIL>';

					$today_date = date("Y-m-d");	

					$one_day_before = date('Y-m-d', strtotime($health_check_date. " - 1 days"));
					

						if(($today_date >= $health_check_date) && ($status == 'AVAILABLE')){
							
							//Create the Transport								
							// $transport = Swift_SmtpTransport::newInstance('***********', 25)
							$transport = Swift_SmtpTransport::newInstance('***********', 25)
							//$transport = Swift_SmtpTransport::newInstance('webmail2014.tm.com.my', 25)
							//->setUsername('ipgen')
							//->setPassword('ipgen001')   //if fail to send email, please update password into IDM
							;

							//Create the Mailer using your created Transport
							 $mailer = Swift_Mailer::newInstance($transport);
						

							//Create a message
							$message = Swift_Message::newInstance('<just bg nama instance>')
							->setSubject('eCOMME : Health Check Due Date Reminder - '.$serial_no.' - '.$master_mme_name)
							->setFrom(array('<EMAIL>' => 'ecomme'))
							->setTo(array($email))
							->setBody(
							'<html>' .
							'<head></head>' .
							'<body>' .
							'Dear '.$fullname.',<br><br>
							Your Item '.$serial_no.' - '.$master_mme_name.' is already approaching due date for Health Check Activity ( '.$health_check_date.' ).
							Kindly log on to COMME website for further details and complete such action.'.'<br>			
							eCOMME : https://ifme.tm.com.my/eCOMME/ </br>'.								
							'<br>Login using GEMS Username and Password'.
							'<br><br>Thank you.'.
							'</body>' .
							'</html>',
							'text/html' // Mark the content-type as HTML
							)
							;
							//Send the message
							$result = $mailer->send($message);
						}						
					//}

									
					if($today_date >= $calibration_due_date && $status == "PENDING CALIBRATION" ){
						
						//Create the Transport
						// $transport = Swift_SmtpTransport::newInstance('***********', 25)
						$transport = Swift_SmtpTransport::newInstance('***********', 25)
						//$transport = Swift_SmtpTransport::newInstance('webmail2014.tm.com.my', 25)
						//->setUsername('ipgen')
						//->setPassword('ipgen001')   //if fail to send email, please update password into IDM
						;

						//Create the Mailer using your created Transport
						$mailer = Swift_Mailer::newInstance($transport);
						

						//Create a message
						$message = Swift_Message::newInstance('<just bg nama instance>')
						->setSubject('eCOMME : Master MME Calibration Activity Reminder - '.$serial_no.' - '.$master_mme_name)
						->setFrom(array('<EMAIL>' => 'ecomme'))
						->setTo(array($email))
						->setBody(
						'<html>' .
						'<head></head>' .
						'<body>' .
						'Dear '.$fullname.',<br><br>
						Your Master MME Item '.$serial_no.' - '.$master_mme_name.' is already due for Calibration Activity ( '.$calibration_due_date.' ).
						Kindly log on to COMME website for further details and complete such action.'.'<br>
						eCOMME : https://ifme.tm.com.my/eCOMME/</br>'.								
						'<br>Login using GEMS Username and Password'.
						'<br><br>Thank you.'.
						'</body>' .
						'</html>',
						'text/html' // Mark the content-type as HTML
						)
						;
						//Send the message
						$result = $mailer->send($message);			
						
					}		
				
				}
			echo "SUCCESS2";
		}
		

}
else{
		echo "Connection could not be established.<br />";
		die( print_r( sqlsrv_errors(), true));
	}

?>