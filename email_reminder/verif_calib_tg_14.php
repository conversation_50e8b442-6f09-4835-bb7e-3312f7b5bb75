

<?php

//error_reporting(0);
require_once 'swift_required.php';
date_default_timezone_set('Asia/Kuala_Lumpur');
header('Content-Type: application/json;charset=utf-8'); //json header
ini_set('max_execution_time', 60);

//include '../db/connection.php';
$serverName = "172.20.49.37"; //serverName\instanceName
$connectionInfo = array( "Database"=>"COMME", "UID"=>"comme", "PWD"=>"%user456");
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	
    // Retrieve email owner test gear
    // Main datatable

        $sql = "SELECT DISTINCT USERS.email as usermail
                FROM MME_ASSET,USERS
                WHERE MME_ASSET.Verification_By = USERS.staff_id AND MME_ASSET.Verification_By != '' AND MME_ASSET.Health_Check_Date != '1900-01-01' AND USERS.email != ''  AND MME_ASSET.status != 'FAULTY'
                ";
                    
        $stmt = sqlsrv_query( $conn, $sql );
            
        if( $stmt === false) {
            die( print_r( sqlsrv_errors(), true) );		
        }
        
        if(sqlsrv_has_rows($stmt) != 1){ 
          // echo "Invalid Username and Password.";
        }
        else {
            $i = 1;
            $test = [];
            while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
                    
                    $email                = htmlspecialchars($row["usermail"]);	
                    $email = filter_var($row["usermail"],FILTER_SANITIZE_EMAIL);

                    //$email = "<EMAIL>";				
                    
                    if($email != ""){		
                        
                            $today_date = date("Y-m-d");
                            $email_arr[] = array("email"=>$email);
                            
                            
                                $sql2 = "SELECT MME_ASSET.Asset, MME_ASSET.Cost_Center, MME_ASSET.Inventory_Number, MME_ASSET.Asset_Description,MME_ASSET.Asset_Description_editable,
                                    MME_ASSET.Tagging_Date, MME_ASSET.Serial_Number, MME_ASSET.Verification_By, USERS.staff_id, USERS.fullname,MME_ASSET.Health_Check_Date,MME_ASSET.status,MME_ASSET.Verif_Calib_Date, MME_ASSET.region, USERS.email, USERS.supervisor_email, USERS.supervisor_name  
                                    FROM MME_ASSET,USERS
                                    WHERE MME_ASSET.Verification_By = USERS.staff_id AND MME_ASSET.Verification_By != '' AND MME_ASSET.Health_Check_Date != '1900-01-01' AND USERS.email = '$email'  AND MME_ASSET.status != 'FAULTY' ";
                                $stmt2 = sqlsrv_query( $conn, $sql2 );
            
                                if( $stmt2 === false) {
                                    //die( print_r( sqlsrv_errors(), true) );		
                                }
                                
                                if(sqlsrv_has_rows($stmt2) != 1){ 
                                  // echo "Invalid Username and Password.";
                                }
                                else {
                                    while( $row2 = sqlsrv_fetch_array( $stmt2, SQLSRV_FETCH_ASSOC) ) {
                                        
                                        $asset                = htmlspecialchars($row2["Asset"]);
                                        $cost_center          = htmlspecialchars($row2["Cost_Center"]);
                                        $inventory_name       = htmlspecialchars($row2["Asset_Description"])." ".htmlspecialchars($row2["Asset_Description_editable"]);
                                        $serial_number        = htmlspecialchars($row2["Serial_Number"]);
                                        $health_check_date    = date_format($row2["Health_Check_Date"],'Y-m-d');
                                        $verif_calib_date     = date_format($row2["Verif_Calib_Date"],'Y-m-d');
                                        $verification_by      = htmlspecialchars($row2["Verification_By"]);
                                        $status               = htmlspecialchars($row2["status"]);
                                        $staff_id             = htmlspecialchars($row2["staff_id"]);
                                        $fullname             = htmlspecialchars($row2["fullname"]);	
                                        $supervisor_email     = htmlspecialchars($row2["supervisor_email"]);
                                        $supervisor_name      = htmlspecialchars($row2["supervisor_name"]);	
                                                
                                        
                                        $today_date = date("Y-m-d");
                                        $three_months_before = date('Y-m-d', strtotime($verif_calib_date. "-3 Months"));
                                        $two_months_before = date('Y-m-d', strtotime($verif_calib_date. "-2 Months"));
                                        $onemonths_before = date('Y-m-d', strtotime($verif_calib_date. "-1 Months"));
                                        


                                        // Convert the date string to a Unix timestamp
                                        $timestamp = strtotime($verif_calib_date);
                                        // Subtract 14 days from the timestamp
                                        $date_in_range_14_days = date('Y-m-d', strtotime('-14 days', $timestamp));
                                        $date_in_range_7_days = date('Y-m-d', strtotime('-7 days', $timestamp));
                                        // Calculate the total count of days between the current date and the health check date
                                        $total_days = date_diff(date_create($today), date_create($verif_calib_date))->format('%a');

                                        // if ($today_date > $date_in_range_14_days && $today_date < $verif_calib_date) {
                                        if ($today_date == $date_in_range_14_days) {
                                            $test[$email][] = array("asset"=>$asset,"serial_number"=>$serial_number,"inventory_name"=>$inventory_name, "verif_calib_date" => $verif_calib_date, "fullname"=>$fullname,"supervisor_name"=>$supervisor_name,"supervisor_email"=>$supervisor_email, "total_days"=>$total_days);
                                            $activity = '14_days_in_range_before_health';
                                        } else if ($today_date == $date_in_range_7_days) {
                                            $test[$email][] = array("asset"=>$asset,"serial_number"=>$serial_number,"inventory_name"=>$inventory_name, "verif_calib_date" => $verif_calib_date, "fullname"=>$fullname,"supervisor_name"=>$supervisor_name,"supervisor_email"=>$supervisor_email, "total_days"=>$total_days);
                                            $activity = '14_days_in_range_before_health';
                                        }

                                        // if ($verif_calib_date == '2023-05-25') {
                                        // 	echo $asset. '----';
                                        // 	echo $today_date . '----';
                                        // 	echo $verif_calib_date . '----';
                                        // 	echo date('Y-m-d', strtotime('-14 days', $timestamp)). '----';
                                        // 	echo date_diff(date_create($today), date_create($verif_calib_date))->format('%a'). '----';
                                        // 	echo "\n";
                                        // }

                                        // echo "\n";

                                        // if(($today_date >= $verif_calib_date) && ($status == 'PENDING VERIFICATION')){
                                        // 	//array_push($email_arr,$asset);
                                            
                                        // 	//$test[$email][]["asset"] = $asset;
                                        // 	$test[$email][] = array("asset"=>$asset,"serial_number"=>$serial_number,"inventory_name"=>$inventory_name, "verif_calib_date" => $verif_calib_date, "fullname"=>$fullname);
                                        // 	//$test[$email]["serial_no"] = $asset;
                                            
                                        // 	$activity = 'exact_date';
                                            
                                        // }
                                        // else if(($today_date == $three_months_before) || ($today_date == $two_months_before) || ($today_date == $one_months_before) ){
                                        // 	$test[$email][] = array("asset"=>$asset,"serial_number"=>$serial_number,"inventory_name"=>$inventory_name, "verif_calib_date" => $verif_calib_date, "fullname"=>$fullname);
                                        // 	//$test[$email]["serial_no"] = $asset;
                                            
                                        // 	$activity = 'earlier_reminder';
                                        // }
                                    }
                                }								
                        //}						
                    }
                    
            }		

            for($i=0; $i<sizeof($email_arr); $i++){
            //for($i=0; $i<10; $i++){
                
                $email = $email_arr[$i]["email"];  //737 kamal  , 416 Famiera
                $data = [];
                $proceed = '0';
                for($j=0; $j<sizeof($test[$email]); $j++){
                    $asset = $test[$email][$j]["asset"];
                    $serial_number = $test[$email][$j]["serial_number"];
                    $inventory_name = $test[$email][$j]["inventory_name"];
                    $verif_calib_date = $test[$email][$j]["verif_calib_date"];
                    $fullname = $test[$email][$j]["fullname"];
                    $total_days = $test[$email][$j]["total_days"];
                    $supervisor_email = $test[$email][$j]["supervisor_email"];

                    if($asset != ""){
                        //$data[] = "<tr><td>".$asset."</td><td>".$serial_number."</td><td>".$inventory_name."</td><td>".$health_check_date."</td></tr>";
                        $data[] = array("asset"=>$asset,"serial_number"=>$serial_number, "inventory_name"=>$inventory_name, "verif_calib_date"=>$verif_calib_date );
                        //$proceed = '1';
                    }
                    
                }
                
                if(!empty($data)){
                    $proceed = '1';
                }
                
                if($proceed == "1"){					
                    $input = '<table style="border: 1px solid black;" >
                    <tr style="border: 1px solid black;">
                        <td><b>Asset Number</b></td>
                        <td><b>Serial Number</b></td>
                        <td><b>Inventory Name</b></td>
                        <td><b>Verif/Calib Due Date</b></td>
                    </tr>';
                    foreach($data as $value){					
                        $input .='<tr style="border: 1px solid black;">
                                <td> '.$value["asset"].' </td>
                                <td> '.$value["serial_number"].' </td>
                                <td> '.$value["inventory_name"].' </td>
                                <td> '.$value["verif_calib_date"].' </td>
                                </tr>';
                    }
                    
                    $input .='</table>';
                
                    // $transport = Swift_SmtpTransport::newInstance('***********', 25)
                    $transport = Swift_SmtpTransport::newInstance('***********', 25)
                    ;
                    
                    //Create the Mailer using your created Transport
                    $mailer = Swift_Mailer::newInstance($transport);
                    //TESTING
                    //$email = "<EMAIL>";
                    
                    if ($supervisor_email) {
                        $cc_address = array($supervisor_email,'<EMAIL>');
                    } else {
                        $cc_address = array('<EMAIL>');
                    }

                    //Create a message
                    $message = Swift_Message::newInstance('<just bg nama instance>')
                    ->setSubject('e-COMME : Test Gear Verification/Calibration Activity Reminder')
                    ->setFrom(array('<EMAIL>' => 'ecomme'))
                    ->setTo(array($email))
                    ->setCc($cc_address)
                    ->setBody(
                    '<html>' .
                    '<head></head>' .
                    '<body>' .
                    'Dear '.$fullname.',<br><br>Below Test Gear will be due in '.$total_days.' days.<br><br>
                    '.$input.'<br><br>
                    Kindly log on to eCOMME website for further details'.'<br>			
                    eCOMME : https://ifme.tm.com.my/eCOMME/  </br>'.									
                    '<br>Login using GEMS Username and Password'.
                    '<br><br>Thank you.'.
                    '</body>' .
                    '</html>',
                    'text/html' // Mark the content-type as HTML
                    )
                    ;
                    
                    
                    //Send the message
                    //echo $asset.$email;
                    echo $result = $mailer->send($message); 
                    // die();
                    
                }				
                
            }
            echo "SUCCESS";
            
            //print_r($data);
    

        }	
        
        
}
else{
        echo "Connection could not be established.<br />";
        die( print_r( sqlsrv_errors(), true));
    }

?>

