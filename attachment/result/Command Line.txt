novatrn@***********

Crest
NovaTraining

R3 EUT Execution


#########Server **************###########

MYSQL_DUMP = mysqldump -u root -p vpn > /root/dbbackup/vpn.sql
	       = mysqldump -u root -p request_system > /root/dbbackup/request_system.sql


iPGEN_Prod = *********** (Folder iPGEN) = scp -r /var/www/html/iPGEN/ administrator@***********:/home/<USER>/IPGEN_BACKUP/
iPGEN_Prod = *********** (Folder Configuration) = scp -r /var/www/html/configuration/ administrator@***********:/home/<USER>/IPGEN_BACKUP/
iPGEN_Prod = *********** (Folder Response) = scp -r /var/www/html/response/ administrator@***********:/home/<USER>/IPGEN_BACKUP/
iPGEN_Prod = *********** (Folder Log) = scp -r /var/www/html/log/ administrator@***********:/home/<USER>/IPGEN_BACKUP/
iPGEND_Prod = *********** (Database) = scp -r /root/dbbackup/ administrator@***********:/home/<USER>/IPGEN_BACKUP/


#########Server ***********###########

*********** (IPGEN_BACKUP) = ********** (iPGEN_Prod_Backup) = scp -r /home/<USER>/IPGEN_BACKUP/ root@**********:/var/www/html/iPGEN_Prod_BACKUP/
*********** (IPGEN_BACKUP) = ********** (iPGEN_Prod_Backup) = scp -r /home/<USER>/IPGEN_BACKUP/ root@**********:/var/www/html/iPGEN_Prod_BACKUP/








