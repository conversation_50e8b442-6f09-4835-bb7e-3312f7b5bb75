<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';


$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	


			$master_mme_asset_no            = filter_var($_POST['master_mme_asset_no'], FILTER_SANITIZE_STRING);
			$master_cost_center             = filter_var($_POST['master_cost_center'], FILTER_SANITIZE_STRING);
			$master_inventory_name          = filter_var($_POST['master_inventory_name'], FILTER_SANITIZE_STRING);
			$master_manufacturer            = filter_var($_POST['master_manufacturer'], FILTER_SANITIZE_STRING);
			$master_model                   = filter_var($_POST['master_model'], FILTER_SANITIZE_STRING);
			$master_user                    = filter_var($_POST['master_user'], FILTER_SANITIZE_STRING);
			$master_serial_no               = filter_var($_POST['master_serial_no'], FILTER_SANITIZE_STRING);
			$master_next_health_check_date  = filter_var($_POST['master_next_health_check_date'], FILTER_SANITIZE_STRING);
			$master_next_calib_date         = filter_var($_POST['master_next_calib_date'], FILTER_SANITIZE_STRING);
			$master_vc                      = filter_var($_POST['master_vc'], FILTER_SANITIZE_STRING);
			$master_vc_location             = filter_var($_POST['master_vc_location'], FILTER_SANITIZE_STRING);
			$master_test_gear_owner         = filter_var($_POST['master_test_gear_owner'], FILTER_SANITIZE_STRING);
			$master_test_gear_status        = filter_var($_POST['master_test_gear_status'], FILTER_SANITIZE_STRING);
			$master_test_gear_remark        = filter_var($_POST['master_test_gear_remark'], FILTER_SANITIZE_STRING);
			$master_region        = filter_var($_POST['master_region'], FILTER_SANITIZE_STRING);
			


			
		
			 $sql = "UPDATE [MME_MASTER_ASSET2]
					SET 
					cost_center='$master_cost_center',
					master_mme_name='$master_inventory_name',
					manufacturer='$master_manufacturer',
					model='$master_model',
					[user]='$master_user',
					serial_no='$master_serial_no',
					health_check_date='$master_next_health_check_date',
					calibration_due_date='$master_next_calib_date',
					vc='$master_vc',
					vc_location='$master_vc_location',
					staff_id='$master_test_gear_owner',
					status='$master_test_gear_status',
					region='$master_region',
					remarks='$master_test_gear_remark'		
					WHERE master_mme_asset_id='$master_mme_asset_no'";
			
		
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				//die( print_r( sqlsrv_errors(), true));
				echo $result = json_encode(array('data' => 'FAILED'));	
			}else{
				echo $result = json_encode(array('data' => 'SUCCESS'));	
			}	

}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
	}

?>