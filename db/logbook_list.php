<?php



header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';


$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	
	//$mme_asset_id = $_GET['mme_assetid'];
	$mme_asset_id      = filter_var($_GET['mme_assetid'], FILTER_SANITIZE_STRING); 
	//sqlinjection
	//$mme_asset_id         = $_GET['mme_assetid'];
	
	$sql = "SELECT USERS_LOG.Staff_id, USERS_LOG.Purpose, USERS_LOG.Remark, USERS_LOG.Verified_by,
			USERS_LOG.Return_date,USERS_LOG.Borrow_date,USERS_LOG.Asset_No,USERS_LOG.LogID,
			[USERS].fullname,[USERS].email
			from USERS_LOG,[USERS]
			WHERE USERS_LOG.Staff_id = [USERS].staff_id AND USERS_LOG.Asset_No = '".$mme_asset_id."'
			ORDER BY LogID DESC";
			
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false) {
				die( print_r( sqlsrv_errors(), true) );
				//echo $inventory = json_encode(array('data' => 'FAILED'));				
			}
			
			if(sqlsrv_has_rows($stmt) != 1){ 
			
				echo '{
					"sEcho": 1,
					"iTotalRecords": "0",
					"iTotalDisplayRecords": "0",
					"data": []
				}';                                 
			
			}
			else {
				$id = 1;
				
				while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {	
						// extract($row);
						// echo $Return_date
						// die();
						$log_id                     = $id++;
						$staff_id                = htmlspecialchars($row["Staff_id"]);
						$staff_name              = htmlspecialchars($row["fullname"]);
						$staff_email             = $row["email"];
						$purpose                    = htmlspecialchars($row["Purpose"]);
						$remark                    = htmlspecialchars($row["Remark"]);
						if($remark == 'undefined'){
							$remark = '';
						}
						$verified_by             = htmlspecialchars($row["Verified_by"]);
						$asset_number            = htmlspecialchars($row["Asset_No"]);
						//$return_date           = date_format($row["Return_date"],'d-M-Y');
						if ($row["Return_date"] == null ) { $return_date = ""; } else { $return_date = date_format($row["Return_date"],'d-M-Y'); } 
						$loan_date               = date_format($row["Borrow_date"],'d-M-Y');

						


						$data[] = array( 
									$staff_id,
									$staff_name,
									$staff_email, 
									$purpose,
									$verified_by,
									$asset_number,
									$loan_date,
									$return_date,
									$remark);			
				}		
				echo $inventory = json_encode(array('data' => $data));
			}	

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
}

?>