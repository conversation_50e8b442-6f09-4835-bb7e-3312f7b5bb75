<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header
include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	

		session_start();
			
		$staff_id = $_SESSION["staff_id"];
		
		$sql = "SELECT USERS.department
				FROM USERS
				WHERE USERS.staff_id = '$staff_id'";
					
		$stmt = sqlsrv_query( $conn, $sql );
			
		if( $stmt === false) {
			die( print_r( sqlsrv_errors(), true) );		
		}
		
		if(sqlsrv_has_rows($stmt) != 1){ 
		// echo "Invalid Username and Password.";
		echo '{
					"sEcho": 1,
					"iTotalRecords": "0",
					"iTotalDisplayRecords": "0",
					"data": []
				}';
		}
		else {
			$i = 1;
			while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
				$division                = $row["department"];
			}
		}

		$sql = "SELECT DISTINCT MME_MASTER_ASSET2.*, USERS.department
				FROM MME_MASTER_ASSET2
				INNER JOIN USERS ON MME_MASTER_ASSET2.staff_id=USERS.staff_id AND USERS.staff_id = '$staff_id'
				";
					
		$stmt = sqlsrv_query( $conn, $sql );
			
		if( $stmt === false) {
			die( print_r( sqlsrv_errors(), true) );		
		}
		
		if(sqlsrv_has_rows($stmt) != 1){ 
			echo '{
				"sEcho": 1,
				"iTotalRecords": "0",
				"iTotalDisplayRecords": "0",
				"data": []
			}';
		}
		else {
			$i = 1;
			while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
					$id                              = $i++;
					$master_mme_asset_id             = htmlspecialchars($row["master_mme_asset_id"]);
					$vc                              = htmlspecialchars($row["vc"]);
					$vc_location                     = htmlspecialchars($row["vc_location"]);					
					$master_mme_name                 = htmlspecialchars($row["master_mme_name"]);	
					$serial_number                   = htmlspecialchars($row["serial_no"]);	
					$cost_center                     = htmlspecialchars($row["cost_center"]);
					$health_check_date    = date_format($row["health_check_date"],'d-M-Y');
					if($health_check_date == "01-Jan-1900"){
						$health_check_date = "";
					}
					$calib_date     = date_format($row["calibration_due_date"],'d-M-Y');
					if($verif_calib_date == "01-Jan-1900"){
						$verif_calib_date = "";
					}
					$owner                            = htmlspecialchars($row["staff_id"]);
					$status                           = htmlspecialchars($row["status"]);
					$division                         = htmlspecialchars($row["department"]);
					if ($division == null ) { $division = "NULL"; } else { $division = htmlspecialchars($row["department"]); } 
					
					
					$data[] = array($id, 
								$vc,
								$cost_center,
								$master_mme_asset_id,
								$master_mme_name, 
								$serial_number,								
								$health_check_date,
								$calib_date,
								$status,						
								$vc_location);			
			}		
			echo $inventory = json_encode(array('data' => $data));
		}	

}
else{
		echo "Connection could not be established.<br />";
		// die( print_r( sqlsrv_errors(), true));
	}

?>