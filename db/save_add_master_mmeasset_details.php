<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	



			$master_mme_asset_no            = filter_var($_POST['master_mme_asset_no'], FILTER_SANITIZE_STRING);
			$master_cost_center             = filter_var($_POST['master_cost_center'], FILTER_SANITIZE_STRING);
			$master_inventory_name          = filter_var($_POST['master_inventory_name'], FILTER_SANITIZE_STRING);
			$master_manufacturer            = filter_var($_POST['master_manufacturer'], FILTER_SANITIZE_STRING);
			$master_model                   = filter_var($_POST['master_model'], FILTER_SANITIZE_STRING);
			$master_user                    = filter_var($_POST['master_user'], FILTER_SANITIZE_STRING);
			$master_serial_no               = filter_var($_POST['master_serial_no'], FILTER_SANITIZE_STRING);
			$master_next_health_check_date  = filter_var($_POST['master_next_health_check_date'], FILTER_SANITIZE_STRING);
			$master_next_calib_date         = filter_var($_POST['master_next_calib_date'], FILTER_SANITIZE_STRING);
			$master_vc                      = filter_var($_POST['master_vc'], FILTER_SANITIZE_STRING);
			$master_vc_location             = filter_var($_POST['master_vc_location'], FILTER_SANITIZE_STRING);
			$master_test_gear_owner         = filter_var($_POST['master_test_gear_owner'], FILTER_SANITIZE_STRING);
			$master_test_gear_status        = filter_var($_POST['master_test_gear_status'], FILTER_SANITIZE_STRING);
			$master_test_gear_remark        = filter_var($_POST['master_test_gear_remark'], FILTER_SANITIZE_STRING);
			$master_region                  = filter_var($_POST['master_region'], FILTER_SANITIZE_STRING);
	

			$sql = "INSERT INTO [MME_MASTER_ASSET2] (
					master_mme_asset_id,
					cost_center,
					master_mme_name,
					manufacturer,
					model,
					[user],
					serial_no,
					health_check_date,
					calibration_due_date,
					vc,
					vc_location,
					staff_id,
					status,
					remarks,
					region
				)
				VALUES (
					'$master_mme_asset_no',
					'$master_cost_center',
					'$master_inventory_name',
					'$master_manufacturer',
					'$master_model',
					'$master_user',
					'$master_serial_no',
					'$master_next_health_check_date',
					'$master_next_calib_date',
					'$master_vc',
					'$master_vc_location',
					'$master_test_gear_owner',
					'$master_test_gear_status',
					'$master_test_gear_remark',
					'$master_region'
				);
				";
			
		
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				die( print_r( sqlsrv_errors(), true));
				echo $result = json_encode(array('data' => 'FAILED'));	
			}else {
				echo $result = json_encode(array('data' => 'SUCCESS'));	
			}

}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
	}

?>