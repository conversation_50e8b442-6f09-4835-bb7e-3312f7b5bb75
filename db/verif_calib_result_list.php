<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';


$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
		
		$sql = "SELECT * FROM MME_VERIFICATION_CALIBRATION";
			
		$stmt = sqlsrv_query( $conn, $sql );
		
		if( $stmt === false) {
			die( print_r( sqlsrv_errors(), true) );
		}
		
		if(sqlsrv_has_rows($stmt) != 1){ 
		   echo '{
				"sEcho": 1,
				"iTotalRecords": "0",
				"iTotalDisplayRecords": "0",
				"data": []
			}';
		}
		else {
			$i = 1;
			while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
					$id                   = $i++;
					$asset                  = htmlspecialchars($row["Asset"]);
					$inventory_name          = htmlspecialchars($row["Inventory_Name"]);
					$activity                = htmlspecialchars($row["activity"]);
					$old_result                = htmlspecialchars($row["status_verif"]);
					$result                  = htmlspecialchars($row["status_activity"]);
					if($result == NULL){
						$result = $old_result;
					}
					$attachment             = htmlspecialchars($row["attachment"]);
					$date                  = date_format($row["Verification_date"],'d-M-Y');
					$performed_by             = htmlspecialchars($row["performed_by"]);
				

					$data[] = array($id, 
								$asset,
								$inventory_name, 
								$date, 
								$activity,
								$result,
								$attachment,
								$performed_by);
									
			}		
			echo $inventory = json_encode(array('data' => $data));
		}

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		die( print_r( sqlsrv_errors(), true));
}

?>