<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header
include 'connection.php';


$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	
				$workgroup         = filter_var($_GET['workgroup'], FILTER_SANITIZE_STRING);	
				//$workgroup = 'SFC & BPQM';
				
				
				if($workgroup == "ADMIN"){	
				
					// storing  request (ie, get/post) global array to a variable  
					$requestData= $_REQUEST;
					
					$columns = array( 
					// datatable column index  => database column name
						0 =>'id', 
						1 =>'asset',
						3=> 'cost_center',
						4=> 'inventory_Number',
						5=> 'asset_desc',
						6=> 'asset_desc_editable',
						7=> 'quantity',
						8=> 'address1',
						9=> 'acquired_date',
						10=> 'tagging_date',
						11=> 'serial_number',
						12=> 'asset_verif_status',
						13=> 'verification_by',
						14=> 'equipment_by',
						15=> 'useful_life_month',
						16=> 'status',
						17=> 'unit',
						18=> 'territory',
						19=> 'health_check_date',
						20=> 'region',
						21=> 'address1',
						22=> 'address2',
						23=> 'address3',
						24=> 'address4'
					);
				
					// getting total number records without any search
					$sql = "SELECT * FROM MME_ASSET";
					$stmt = sqlsrv_query( $conn, $sql );
				
					$totalData = sqlsrv_has_rows($stmt);
					$totalFiltered = $totalData;  // when there is no search parameter then total number rows = total number filtered rows.

				
					$sql = "SELECT * FROM MME_ASSET WHERE 1=1 ";
					// echo $sql;
					$stmt = sqlsrv_query( $conn, $sql );
					$totalFiltered = sqlsrv_has_rows($stmt); // when there is a search parameter then we have to modify total number filtered rows as per search result. 
					//$sql.=" ORDER BY ". $columns[$requestData['order'][0]['column']]."   ".$requestData['order'][0]['dir']."  LIMIT ".$requestData['start']." ,".$requestData['length']."   ";
					/* $requestData['order'][0]['column'] contains colmun index, $requestData['order'][0]['dir'] contains order such as asc/desc  */	
					//echo $sql;	
				
					if( !empty($requestData['search']['value']) ) {
						$sql.=" AND ( asset LIKE '".$requestData['search']['value']."%' ";    
						$sql.=" OR cost_center LIKE '".$requestData['search']['value']."%' ";
						$sql.=" OR inventory_Number LIKE '".$requestData['search']['value']."%' )";
					}

					$sql.=" ORDER BY ". $columns[$requestData['order'][0]['column']]."   ".$requestData['order'][0]['dir']."  LIMIT ".$requestData['start']." ,".$requestData['length']."   ";

					$data = array();
					$query = "SELECT *	FROM MME_ASSET WHERE 1=1";

					//echo $query;
					$i=1;			
					while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
						
						$a = $row["Asset"];
						$nestedData=array(); 
						$nestedData[] = $i;
						$nestedData[] = $a;
						//$nestedData[] = "<button type='button' class='btn btn-sm btn-table' name='mme_asset_edit_btn' id='$a'>$a</button>";
						$nestedData[] = htmlspecialchars($row["Cost_Center"]);
						$nestedData[] = htmlspecialchars($row["Inventory_Number"]);
						$nestedData[] = htmlspecialchars((string)$row["Asset_Description"]);
						$nestedData[] = htmlspecialchars((string)$row["Asset_Description_editable"]);
						$nestedData[] = htmlspecialchars($row["Quantity"]);
						$nestedData[] = htmlspecialchars($row["address1"]." ".$row["address2"]." ".$row["address3"]." ".$row["address4"]);						
						// $nestedData[] = htmlspecialchars(trim($row["address1"]));
						// $nestedData[] = htmlspecialchars($row["address3"]);
						// $nestedData[] = htmlspecialchars($row["address4"]);
						$nestedData[] = date_format($row["First_Acquired_On"],'d-M-Y');
						$nestedData[] = date_format($row["Tagging_Date"],'d-M-Y');
						$nestedData[] = htmlspecialchars($row["Serial_Number"]);
						$nestedData[] = htmlspecialchars($row["Asset_Verification_Status"]);
						//$nestedData[] = trim($row["Verification_By"]);
						//$nestedData[] = trim((string)$row["Verification_By"]);
						$nestedData[] = htmlspecialchars(trim($row["Verification_By"]));
						$nestedData[] = htmlspecialchars((string)$row["Equipment_Model"]);
						$nestedData[] = htmlspecialchars($row["Useful_Life_month"]);
						$nestedData[] = htmlspecialchars($row["status"]);
						$nestedData[] = htmlspecialchars($row["unit"]);
						$nestedData[] = htmlspecialchars($row["territory"]);
						$nestedData[] = date_format($row["Health_Check_Date"],'d-M-Y');
						$nestedData[] = date_format($row["Verif_Calib_Date"],'d-M-Y');
						$nestedData[] = htmlspecialchars($row["region"]);
						$nestedData[] = htmlspecialchars(trim($row["address1"]));
						$nestedData[] = htmlspecialchars(trim($row["address2"]));
						$nestedData[] = htmlspecialchars($row["address3"]);
						$nestedData[] = htmlspecialchars($row["address4"]);					
						
						$data[] = $nestedData;
						
						$i++;
						//var_dump($data);
						
					} 	

					$json_data = array(
								"draw"            => intval( $requestData['draw'] ),   
								"recordsTotal"    => intval( $totalData ),  
								"recordsFiltered" => intval( $totalFiltered ), 
								"data"            => $data  
								);
			
					//echo $inventory = json_encode(array('data' => $data));
			
					echo json_encode($json_data);			
				
				} else {
				
					$sql = "SELECT MME_ASSET.Asset,
						MME_ASSET.Health_Check_Date,
						MME_ASSET.Verif_Calib_Date,
						MME_ASSET.Cost_Center,
						MME_ASSET.Inventory_Number,
						MME_ASSET.Asset_Description,
						MME_ASSET.Asset_Description_editable,
						MME_ASSET.Quantity,
						MME_ASSET.address1,
						MME_ASSET.address2,
						MME_ASSET.address3,
						MME_ASSET.address4,
						MME_ASSET.First_Acquired_On,
						MME_ASSET.Tagging_Date,
						MME_ASSET.Serial_Number,
						MME_ASSET.Asset_Verification_Status,
						MME_ASSET.Verification_By,
						MME_ASSET.Equipment_Model,
						MME_ASSET.Useful_Life_month,
						MME_ASSET.status,				
						MME_ASSET.unit,			
						MME_ASSET.territory,			
						MME_ASSET.region			
						FROM MME_ASSET
						WHERE MME_ASSET.region = '$workgroup'";
					
					
					
						
			
					$stmt = sqlsrv_query( $conn, $sql );
					
					if( $stmt === false) {
						die( print_r( sqlsrv_errors(), true) );		
					}
					
					if(sqlsrv_has_rows($stmt) != 1){ 
					  // echo "Invalid Username and Password.";
					}
					else {
						$i=1;
						while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
								$id                   = $i;
								$a                    = $row["Asset"];
								//$asset                = "<button type='button' class='btn btn-sm btn-table' name='mme_asset_edit_btn' id='$a'>$a</button>";
								$asset                = $row["Asset"];
								$cost_center          = $row["Cost_Center"];
								$inventory_Number     = $row["Inventory_Number"];
								$asset_desc           = (string)$row["Asset_Description"];
								$asset_desc_editable  = (string)$row["Asset_Description_editable"];
								$quantity             = $row["Quantity"];
								$address              = $row["address1"]." ".$row["address2"]." ".$row["address3"]." ".$row["address4"];
								$address1             = $row["address1"];
								$address2             = $row["address2"];
								$address3             = $row["address3"];
								$address4             = $row["address4"];
								$acquired_date        = date_format($row["First_Acquired_On"],'d-M-Y');
								$tagging_date         = date_format($row["Tagging_Date"],'d-M-Y');
								$serial_number        = $row["Serial_Number"];
								$asset_verif_status   = $row["Asset_Verification_Status"];
								$verification_by      = trim($row["Verification_By"]);
								$equipment_by         = $row["Equipment_Model"];
								$useful_life_month    = $row["Useful_Life_month"];
								$status               = $row["status"];
								$unit                 = $row["unit"];
								$territory            = $row["territory"];
								$region               = $row["region"];
								$health_check_date    = date_format($row["Health_Check_Date"],'d-M-Y');
								$verif_calib_date     = date_format($row["Verif_Calib_Date"],'d-M-Y');

								$data[] = array($id, 
											//$asset2,
											$asset,
											$cost_center,
											$inventory_Number, 
											$asset_desc,
											$asset_desc_editable,
											$quantity,
											$address,
											$acquired_date,
											$tagging_date,
											$serial_number,   //
											$asset_verif_status,
											$verification_by,
											$equipment_by,
											$useful_life_month,
											$status,
											$unit,
											$territory,
											$health_check_date,
											$verif_calib_date, //
											$region
											// $address2,
											// $address3,
											// $address4,		
											// $address1
										);		

						$i++;
						}		
						echo $inventory = json_encode(array('data' => $data));
					}				
				}
		
}
else{
		echo "Connection could not be established.<br />";
		die( print_r( sqlsrv_errors(), true));
	}

?>