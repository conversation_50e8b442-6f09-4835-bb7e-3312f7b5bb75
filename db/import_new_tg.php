<?php

//echo $_FILES['file']['name'];

include 'connection.php';
$conn = sqlsrv_connect( $serverName, $connectionInfo);

include "../plugin/simplexlsx/SimpleXLSX.php";

//$xlsx = new SimpleXLSX( $_FILES['file']['tmp_name'] );
$filename=$_FILES["file"]["tmp_name"];
// echo $xlsx;

if ($conn){	
  
	if ( $xlsx = SimpleXLSX::parse($filename) ) {
	  $j = 0;  
	  foreach ($xlsx->rows() as $data) {
		$j++;
		if ( $j == 1){
			continue;
		} else {
			$asset_no           = filter_var($data[0], FILTER_SANITIZE_STRING);
			$cost_center        = filter_var($data[1], FILTER_SANITIZE_STRING);
			$inventory_number   = filter_var($data[2], FILTER_SANITIZE_STRING);
			$asset_description  = filter_var($data[3], FILTER_SANITIZE_STRING);
			$asset_description_editable = filter_var($data[4], FILTER_SANITIZE_STRING);
			$quantity           = filter_var($data[5], FILTER_SANITIZE_STRING);
			$address1           = filter_var($data[6], FILTER_SANITIZE_STRING);
			$address2           = filter_var($data[7], FILTER_SANITIZE_STRING);
			$address3           = filter_var($data[8], FILTER_SANITIZE_STRING);
			$address4           = filter_var($data[9], FILTER_SANITIZE_STRING);
			$acquired_date      = filter_var($data[10], FILTER_SANITIZE_STRING);
			$tagging_date       = filter_var($data[11], FILTER_SANITIZE_STRING);
			$serial_no          = filter_var($data[12], FILTER_SANITIZE_STRING);
			$asset_verification_status = filter_var($data[13], FILTER_SANITIZE_STRING);
			$tg_owner           = filter_var($data[14], FILTER_SANITIZE_STRING);
			$equipment_model    = filter_var($data[15], FILTER_SANITIZE_STRING);
			$useful_life_month  = filter_var($data[16], FILTER_SANITIZE_STRING);
			$status             = filter_var($data[17], FILTER_SANITIZE_STRING);
			$unit               = filter_var($data[18], FILTER_SANITIZE_STRING);
			$territory          = filter_var($data[19], FILTER_SANITIZE_STRING);
			$health_check_date  = filter_var($data[20], FILTER_SANITIZE_STRING);
			$verification_date  = filter_var($data[21], FILTER_SANITIZE_STRING);
			$region             = filter_var($data[22], FILTER_SANITIZE_STRING);
				
			// for ($i=0; $i<23;$i++){
			// 	echo $data[$i];
			// }

			$sql = "SELECT [MME_ASSET].Asset FROM [MME_ASSET] WHERE Asset ='$asset_no'";
			$stmt = sqlsrv_query( $conn, $sql );
			if ($stmt) {					
				$rows = sqlsrv_has_rows( $stmt );	
				if ($rows === true){
					$sql2 = "UPDATE [MME_ASSET]
							SET 
							Cost_Center='$cost_center',
							Inventory_Number='$inventory_number',
							Asset_Description='$asset_description',
							Asset_Description_editable='$asset_description_editable',
							Quantity='$quantity',
							address1='$address1',
							address2='$address2',
							address3='$address3',
							address4='$address4',
							First_Acquired_On='$acquired_date',
							Tagging_Date='$tagging_date',
							Serial_Number='$serial_no',
							Asset_Verification_Status='$asset_verification_status',
							Verification_By='$tg_owner',
							Equipment_Model='$equipment_model',
							Useful_Life_month='$useful_life_month',
							status='$status',
							unit='$unit',
							territory='$territory',								
							Health_Check_Date='$health_check_date',
							Verif_Calib_Date='$verification_date',
							region='$region'								
							WHERE Asset='$asset_no'";
				}
				else{
					$sql2 = "INSERT INTO [MME_ASSET] ( Asset,Cost_Center,Inventory_Number,Asset_Description,Asset_Description_editable,
												Quantity,address1,address2,address3,address4,
												First_Acquired_On,Tagging_Date,
												Serial_Number,
												Asset_Verification_Status,
												Verification_By,
												Equipment_Model,
												Useful_Life_month,
												status,
												unit,									
												territory,
												Health_Check_Date,
												Verif_Calib_Date,
												region
												) VALUES 
											  ('$asset_no',
											  '$cost_center',
											  '$inventory_number',
											  '$asset_description',
											  '$asset_description_editable',
											  '$quantity',
											  '$address1',
											  '$address2',
											  '$address3',
											  '$address4',
											  '$acquired_date',
											  '$tagging_date',
											  '$serial_no',
											  '$asset_verification_status',
											  '$tg_owner',
											  '$equipment_model',
											  '$useful_life_month',
											  '$status',
											  '$unit',
											  '$territory',
											  '$health_check_date',
											  '$verification_date',
											  '$region')";												  
					
				}
				
				$stmt2 = sqlsrv_query( $conn, $sql2 );
				if( $stmt2 === false ) {						
					die( print_r( sqlsrv_errors(), true));
					$result = 'FAILED';
				}else{
					$result = 'SUCCESS';	
				}
			}
		}
	  }
	  echo $result;
	  //throws a message if data successfully imported to mysql database from excel file
	  sqlsrv_free_stmt( $stmt);
	  sqlsrv_free_stmt( $stmt2);

	} else {
	  echo SimpleXLSX::parseError();
	  echo 'FAILED';
	}
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
	}


?>