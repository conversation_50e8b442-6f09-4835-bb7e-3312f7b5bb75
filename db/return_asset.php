<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	

		
		$returndate     = filter_var($_POST['returndate'], FILTER_SANITIZE_STRING); 
		$mmeassetid     = filter_var($_POST['mmeassetid'], FILTER_SANITIZE_STRING); 
		$type     = filter_var($_POST['type'], FILTER_SANITIZE_STRING); 
		
				
		$sql = "UPDATE [USERS_LOG] 
			SET Remark='CONFIRMATION'
			WHERE LogID IN
			(SELECT TOP (1) LogID FROM [USERS_LOG]
			WHERE Asset_No='$mmeassetid' 
			ORDER BY LogID DESC)";
		
		//$sql .="";
		if($type == "MASTER_TG"){
			$sql .= "UPDATE [MME_MASTER_ASSET2] 
					SET status='CONFIRMATION'
					WHERE master_mme_asset_id='$mmeassetid'";
		} 
		else if ($type == "TG"){
			$sql .= "UPDATE [MME_ASSET] 
					SET status='CONFIRMATION'
					WHERE Asset='$mmeassetid'";
		}
				
		$stmt = sqlsrv_query( $conn, $sql );
		
		if( $stmt === false ) {
			//die( print_r( sqlsrv_errors(), true));
			echo "FAILED";
		}
		echo "SUCCESS";
			

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
}

?>