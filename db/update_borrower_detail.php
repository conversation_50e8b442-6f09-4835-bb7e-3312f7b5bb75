<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

	if( $conn ) {
	
			$borrowerstaffid     = filter_var($_POST['borrowerstaffid'], FILTER_SANITIZE_STRING);
			$dateborrow          = filter_var($_POST['dateborrow'], FILTER_SANITIZE_STRING);
			$purposeborrowing    = filter_var($_POST['purposeborrowing'], FILTER_SANITIZE_STRING);
			$verifiedby          = filter_var($_POST['verifiedby'], FILTER_SANITIZE_STRING);
			$mmeassetid          = filter_var($_POST['mmeassetid'], FILTER_SANITIZE_STRING);
			$type                = filter_var($_POST['type'], FILTER_SANITIZE_STRING);
		
			$sql = "INSERT INTO USERS_LOG ( Staff_id,Purpose,Borrow_date,Verified_by,Asset_No) VALUES 
					('$borrowerstaffid','$purposeborrowing','$dateborrow','$verifiedby','$mmeassetid') "; 	
		
			if( $type == "MASTER_TG"){
				$sql .= "UPDATE [MME_MASTER_ASSET2] 
				SET status='UNAVAILABLE'
				WHERE master_mme_asset_id='$mmeassetid'";
			}

			else if ($type == "TG"){
				$sql .= "UPDATE [MME_ASSET] 
				SET status='UNAVAILABLE'
				WHERE Asset='$mmeassetid'";
			}
			
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				//die( print_r( sqlsrv_errors(), true));
				echo "FAILED";
			}
			echo "SUCCESS";

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		die( print_r( sqlsrv_errors(), true));
}

?>