<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header
include 'connection.php';


$conn = sqlsrv_connect( $serverName, $connectionInfo);

$q       = filter_var($_GET['q'], FILTER_SANITIZE_STRING);

$asset_desc   = $q;

if( $conn ) {	

	//$sql = "SELECT DISTINCT Asset_Description FROM [MME_ASSET] WHERE MME_ASSET.Asset_Description LIKE '%$asset_desc%'";
	$sql = "SELECT DISTINCT Asset_Description FROM [MME_ASSET] WHERE MME_ASSET.Asset_Description LIKE '%".$asset_desc."%'";
				
	$stmt = sqlsrv_query( $conn, $sql );
		
	while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
			//$id                   = $row["id"];
			$Asset_Description      = $row["Asset_Description"];
			//$vci         		  = $row["vci"];
			
			$data[] = array('data' => $Asset_Description);
	}		
	$alldata[] = array('data' => "ALL");	
    $newdata[] = array_merge($alldata,$data);	
	echo $inventory = json_encode($newdata);
}	

?>

