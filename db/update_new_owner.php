<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header
include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
			$asset_id     = filter_var($_POST['asset_id'], FILTER_SANITIZE_STRING); 
			$status       = filter_var($_POST['status'], FILTER_SANITIZE_STRING); 
			$new_tg_owner = filter_var($_POST['new_tg_owner'], FILTER_SANITIZE_STRING); 
			$hc_date      = filter_var($_POST['hc_date'], FILTER_SANITIZE_STRING); 
			$verif_date   = filter_var($_POST['verif_date'], FILTER_SANITIZE_STRING); 
			$old_tg_owner   = filter_var($_POST['old_tg_owner'], FILTER_SANITIZE_STRING); 
			$item           = filter_var($_POST['item'], FILTER_SANITIZE_STRING); 

			if($item == "TG"){
				$sql = "UPDATE [MME_ASSET]
					SET Health_Check_Date='$hc_date',
					Verif_Calib_Date = '$verif_date',
					Verification_By = '$new_tg_owner',
					status='$status'
					WHERE Asset='$asset_id'";

			} else if ($item == "MASTER_TG"){
				$sql = "UPDATE [MME_MASTER_ASSET2]
					SET health_check_date='$hc_date',
					calibration_due_date = '$verif_date',
					staff_id = '$new_tg_owner',
					status='$status'
					WHERE master_mme_asset_id='$asset_id'";
			}
			
		
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				die( print_r( sqlsrv_errors(), true));
			}
			else {
				$sql2 = "SELECT a.supervisor_email,a.fullname,a.supervisor_name,a.email,b.email AS email_1,b.fullname AS fullname_1
					FROM USERS a, USERS b
					WHERE a.staff_id='".$old_tg_owner."' AND b.staff_id='".$new_tg_owner."'";
					
				$stmt = sqlsrv_query( $conn, $sql2 );
				
				if( $stmt === false) {
					echo '{
						"sEcho": 1,
						"iTotalRecords": "0",
						"iTotalDisplayRecords": "0",
						"data": []
					}';	
				} else {
					while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {

							$supervisor_name       = $row["supervisor_name"];
							$supervisor_email      = $row["supervisor_email"];
							$owner_name            = $row["fullname"];							
							$owner_email           = $row["email"];
							$staff_email           = $row["email_1"];
							$staff_name            = $row["fullname_1"];
	
	
						//$data[] = array("",$supervisor_email,$owner_name,$supervisor_name,$owner_email,$staff_email,$staff_name,"");
					}
					
					echo $result = json_encode(array(
						'data' => 'SUCCESS',
						'supervisor_name'=>$supervisor_name,
						'supervisor_email'=>$supervisor_email,
						'owner_name'=>$owner_name,
						'owner_email'=>$owner_email,
						'staff_email'=>$staff_email,
						'staff_name'=>$staff_name		
					));	
				}

				

			}

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		// die( print_r( sqlsrv_errors(), true));
}

?>