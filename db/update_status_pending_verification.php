<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);
$today_date = date("Y-m-d");

if( $conn ) {
	
				
			// $sql = "UPDATE [MME_ASSET]
			// 		SET status='PENDING VERIFICATION'
			// 		WHERE Asset='$asset_id'";

			$sql = 'SELECT * FROM MME_ASSET'; 		
			
			$stmt = sqlsrv_query( $conn, $sql );
			if( $stmt === false) {
				//die( print_r( sqlsrv_errors(), true) );		
			}else {
				while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
					$asset                = htmlspecialchars($row["Asset"]);
					$verif_calib_date     = date_format($row["Verif_Calib_Date"],'Y-m-d');
					$status               = htmlspecialchars($row["status"]);

					if($verif_calib_date < $today_date && $status == "AVAILABLE"){

						$sql2 = "UPDATE [MME_ASSET]
								SET status='PENDING VERIFICATION'
								WHERE Asset='$asset'";

						$stmt2 = sqlsrv_query( $conn, $sql2 );
						if( $stmt2 === false ) {
							die( print_r( sqlsrv_errors(), true));
						}
						sqlsrv_free_stmt( $stmt2);
						$data[] = array($asset, 
								$verif_calib_date,
								$status);


					}
					// $data[] = array($asset, 
					// 			$verif_calib_date,
					// 			$status);

				}
				echo $inventory = json_encode(array('data' => $data));
			}
			
			
		

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		// die( print_r( sqlsrv_errors(), true));
}

?>