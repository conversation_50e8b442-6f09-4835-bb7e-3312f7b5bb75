<?php



header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';


/*
$serverName = "172.20.49.37"; //serverName\instanceName
$connectionInfo = array( "Database"=>"COMME", "UID"=>"comme", "PWD"=>"%user456");*/
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	
		//$staff_id = $_POST["staff_id"];
		$staff_id = filter_var($_POST['staff_id'], FILTER_SANITIZE_STRING);
		//$staff_id     = $_POST['staff_id'];
		//$staff_id = 'TM36261';
		//echo mssql_escape($staff_id);
				$sql = "SELECT * FROM [USERS] WHERE staff_id='".$staff_id."'";
				/*$sql = "SELECT [user].fullname, [user].email, [user].contact_number , [user].staff_id
				FROM [user]
				WHERE [user].staff_id = '$staff_id'";*/
				
				$stmt = sqlsrv_query( $conn, $sql );
				
				if( $stmt === false) {
					die( print_r( sqlsrv_errors(), true) );		
					//echo "There are no rows. <br />"; //add info in db
						/*$sql = "INSERT INTO [USERS] ( staff_id,fullname,email,department,contact_number,designation,supervisor_name) VALUES 
													('$uid','$name','$mail','$unit','$mobile','$position','$report')"; 
													
						$stmt = sqlsrv_query( $conn, $sql );
		
						if( $stmt === false ) {
							die( print_r( sqlsrv_errors(), true));
						}
						
						//session_start();
						$_SESSION["fullname"] = $name;
						$_SESSION["staff_id"] = $uid;
						$_SESSION["password"] = $_POST['password'];
						
						echo $post_data = json_encode(array('status' => 'succeed','staffid' => $uid,'mail' => $mail,'staffname' => $name,'mobile' => $mobile,'unit' => $unit,'position' => $position));
					*/
					
					
				}
				else {
					//$id = 1;
					while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
						
							$id                  = $row["id"];
							$staff_id            = $row["staff_id"];
							$fullname            = strtoupper($row["fullname"]);
							$email               = $row["email"];
							$department          = $row["department"];
							$designation         = $row["designation"];
							$image               = $row["image"];
							$contact             = $row["contact_number"];
							$supervisor_name     = $row["supervisor_name"];
							$supervisor_email    = $row["supervisor_email"];
							
						//$data[] = array($id,$staff_id,$fullname,$email,$department,$designation,$image,$supervisor_name,$supervisor_email,$contact);
					}		
					//echo $userdetails = json_encode(array('data' => $data));
					echo $result = json_encode(array(
						'data' => 'SUCCESS',
						'id'=>$id,
						'staff_id'=>$staff_id,
						'fullname'=>$fullname,
						'email'=>$email,
						'department'=>$department,
						'designation'=>$designation,		
						'image'=>$image,		
						'contact'=>$contact,		
						'supervisor_name'=>$supervisor_name,		
						'supervisor_email'=>$supervisor_email		
					));	
				}	
			

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		die( print_r( sqlsrv_errors(), true));
}

?>