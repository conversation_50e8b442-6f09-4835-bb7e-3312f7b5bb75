<?php



header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';


/*
$serverName = "172.20.49.37"; //serverName\instanceName
$connectionInfo = array( "Database"=>"COMME", "UID"=>"comme", "PWD"=>"%user456");*/
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	
			 /*$staff_id           = $_POST["staff_id"];
			 $email              = $_POST["email"];
			 $contact_number     = $_POST["contact_number"];
			 $image              = $_POST["image"];			 
			 $supervisor_email   = $_POST["supervisor_email"];*/
			 
			 
			 $staff_id         = filter_var($_POST['staff_id'], FILTER_SANITIZE_STRING);
			 //sqlinjection
			 //$staff_id     = $_POST['staff_id'];
			 $email            = filter_var($_POST['email'], FILTER_SANITIZE_STRING);
			 $contact_number   = filter_var($_POST['contact_number'], FILTER_SANITIZE_STRING);
			 $image            = filter_var($_POST['image'], FILTER_SANITIZE_STRING);
			 $supervisor_email = filter_var($_POST['supervisor_email'], FILTER_SANITIZE_STRING);
			
			if($image == "attachment/images/undefined"){
				$sql = "UPDATE [USERS]
					SET email='$email', contact_number='$contact_number', supervisor_email='$supervisor_email'
					WHERE staff_id='".$staff_id."'";				
			}
			else {	
				$upload_image = $image."";
				$sql = "UPDATE [USERS]
						SET email='$email', contact_number='$contact_number', image='$upload_image' , supervisor_email='$supervisor_email'
						WHERE staff_id='".$staff_id."'";					
			}		
			
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				die( print_r( sqlsrv_errors(), true));
			}
			//echo "SUCCESS";
			echo $result = json_encode(array('data' => 'SUCCESS'));
		
			

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		die( print_r( sqlsrv_errors(), true));
}

?>