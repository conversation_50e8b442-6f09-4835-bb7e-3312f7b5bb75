<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);
$workgroup_search    = filter_var($_POST['workgroup_search'], FILTER_SANITIZE_STRING);


if( $conn ) {	
	// Retrieve email owner test gear
	// Main datatable
		/*$sql = "SELECT *					
				FROM MME_ASSET";*/
	if($workgroup_search == "ALL"){
		$sql = "SELECT * FROM USERS";
	}
	else {
		$sql = "SELECT * FROM USERS WHERE workgroup='".$workgroup_search."'";
		
	}
				
					
		$stmt = sqlsrv_query( $conn, $sql );
			
		if( $stmt === false) {
			//die( print_r( sqlsrv_errors(), true) );	
			echo $inventory = json_encode(array('data' => 'FAILED'));	
		}
		
		if(sqlsrv_has_rows($stmt) != 1){ 
		  // echo "Invalid Username and Password.";
			echo '{
				"sEcho": 1,
				"iTotalRecords": "0",
				"iTotalDisplayRecords": "0",
				"data": []
			}';
		}
		else {
			$i = 1;
			while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
					$id                 = $i++;
					$staff_id           = $row["staff_id"];
					$fullname           = $row["fullname"];
					$email              = $row["email"];					
					$workgroup          = $row["workgroup"];
					$department         = $row["department"];
					$contact_number     = $row["contact_number"];
					$designation        = $row["designation"];
					$supervisor_name    = $row["supervisor_name"];
					$supervisor_email   = $row["supervisor_email"];
					$time_stamp   = date_format($row["last_logon"],'Y-m-d H:i');
					if($time_stamp == false){
						$time_stamp = "";
					}
					
					$data[] = array($id, 
								$staff_id,
								$fullname,
								$email,
								$department, 
								$contact_number,
								$designation,
								$supervisor_name,
								//$supervisor_email,
								$workgroup,			
								$time_stamp);			
			}		
			echo $inventory = json_encode(array('data' => $data));
		}	

}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
	}

?>