<?php



header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	$ownerid     = filter_var($_POST['ownerid'], FILTER_SANITIZE_STRING); 
			
		if(!empty($ownerid)) {	
				$sql = "SELECT * FROM [USERS] WHERE staff_id='".$ownerid."'";

				$stmt = sqlsrv_query( $conn, $sql );
				
				if( $stmt === false) {
					//die( print_r( sqlsrv_errors(), true) );	
					echo $ownerdetail = json_encode(array('data' => 'FAILED'));					
				}
				else {
					$id = 1;
					while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
						
							$id                   = $id++;
							$fullname             = $row["fullname"];
							$email                = $row["email"];
							$staff_id             = $row["staff_id"];
							$workgroup            = $row["workgroup"];
							$contact_number       = $row["contact_number"];
					
					}		
						echo $result = json_encode(array(
							'data' => 'SUCCESS',
							'fullname'=>$fullname,
							'email'=>$email,
							'workgroup'=>$workgroup,
							'contact_number'=>$contact_number
						));	
				}	
			}

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
}

?>