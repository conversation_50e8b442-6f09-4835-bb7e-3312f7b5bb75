<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

/*
$serverName = "172.20.49.37"; //serverName\instanceName
$connectionInfo = array( "Database"=>"COMME", "UID"=>"comme", "PWD"=>"%user456");*/
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	
	//$staff_id = $_GET["staff_id"];
	$staff_id  = filter_var($_GET['staff_id'], FILTER_SANITIZE_STRING);

				/*$sql = "SELECT MMEAsset.MMEAssetID, MMEAsset.Asset_No, MMEAsset.Asset_Desc, MMEAsset.Asset_Desc_editable,MMEAsset.Serial_Number 
						FROM MMEAsset
						WHERE MMEAsset.Asset_Desc_editable2 = '$full_name'"; */

				
				/*$sql = "SELECT Test_Gear_Inventory.ID, Test_Gear_Inventory.Asset, Test_Gear_Inventory.Asset_Description, Test_Gear_Inventory.Asset_Description_editable,Test_Gear_Inventory.Serial_Number,Test_Gear_Inventory.status 
						FROM Test_Gear_Inventory,logtracking
						WHERE logtracking.Staff_id LIKE '%$staff_id%'";
				*/
				/*$sql = "SELECT Test_Gear_Inventory.Asset,Test_Gear_Inventory.Asset_Description, Test_Gear_Inventory.Asset_Description_editable,
						Test_Gear_Inventory.Serial_Number,Test_Gear_Inventory.owner_name
						FROM Test_Gear_Inventory,logtracking
						WHERE logtracking.Staff_id = '$staff_id' AND Test_Gear_Inventory.status = 'OUT' AND logtracking.Return_date IS NULL
						ORDER BY logID DESC 
						"; */
				/*$sql = "SELECT MME_ASSET.Asset,MME_ASSET.Asset_Description, MME_ASSET.Asset_Description_editable,
						MME_ASSET.Serial_Number,MME_ASSET.owner_name
						FROM MME_ASSET,USERS_LOG
						WHERE USERS_LOG.Staff_id = '$staff_id' AND MME_ASSET.status = 'UNAVAILABLE' AND USERS_LOG.Return_date IS NULL
						ORDER BY logID DESC ";*/
						
				$sql = "SELECT USERS_LOG.Asset_No, MME_ASSET.Asset_Description, MME_ASSET.Asset_Description_editable, MME_ASSET.Serial_Number, MME_ASSET.Verification_By
						FROM USERS_LOG
						INNER JOIN MME_ASSET ON USERS_LOG.Asset_No=MME_ASSET.Asset
						WHERE USERS_LOG.Staff_id = '$staff_id' AND MME_ASSET.status != 'AVAILABLE' AND USERS_LOG.Return_date IS NULL
						ORDER BY logID DESC";
					
				
				//select * from log_provi where filename LIKE '%PS1026765474%';
				
				$stmt = sqlsrv_query( $conn, $sql );
				
				if( $stmt === false) {
					//die( print_r( sqlsrv_errors(), true) );	
					echo $userdetails = json_encode(array('data' => 'FAILED'));		
				}
				
				if(sqlsrv_has_rows($stmt) != 1){ 
			
					echo '{
						"sEcho": 1,
						"iTotalRecords": "0",
						"iTotalDisplayRecords": "0",
						"data": []
					}';                                 
				
				}
				
				else {
					$i = 1;
					while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {						
						//	$mmeasset_id              = $row["MMEAssetID"];
							$id                       = $i++;
							$asset_no                 = $row["Asset_No"];
							$asset_desc               = $row["Asset_Description"];
							$asset_desc_editable      = $row["Asset_Description_editable"];
							$asset_serialno           = $row["Serial_Number"];
							$asset_owner              = $row["Verification_By"];
							
							//$asset_details = $row["Asset_Desc"] + " " + $row["Asset_Desc_editable"];
							
						$data[] = array($asset_no,$asset_desc."-".$asset_desc_editable,$asset_serialno,$asset_owner);
					}
						//$data[] = array("1",$_POST["full_name"],"3","4");
					echo $userdetails = json_encode(array('data' => $data));
				}

		sqlsrv_free_stmt( $stmt);
}
else{
		//echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
		echo $userdetails = json_encode(array('data' => 'FAILED'));		
}

?>