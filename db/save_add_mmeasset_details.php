<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	


			$asset_no              = filter_var($_POST['mme_asset_no'], FILTER_SANITIZE_STRING);
			$cost_center           = filter_var($_POST['cost_center'], FILTER_SANITIZE_STRING);
			$inventory_name        = filter_var($_POST['inventory_name'], FILTER_SANITIZE_STRING);
			$asset_desc            = filter_var($_POST['asset_desc'], FILTER_SANITIZE_STRING);
			$asset_desc_editable   = filter_var($_POST['asset_desc_editable'], FILTER_SANITIZE_STRING);
			$quantity              = filter_var($_POST['quantity'], FILTER_SANITIZE_STRING);
			$address1              = filter_var($_POST['address1'], FILTER_SANITIZE_STRING);
			$address2              = filter_var($_POST['address2'], FILTER_SANITIZE_STRING);
			$address3              = filter_var($_POST['address3'], FILTER_SANITIZE_STRING);
			$address4              = filter_var($_POST['address4'], FILTER_SANITIZE_STRING);
			$first_acq_date        = filter_var($_POST['first_acq_date'], FILTER_SANITIZE_STRING);
			$tagg_date             = filter_var($_POST['tagg_date'], FILTER_SANITIZE_STRING);
			$serial_no             = filter_var($_POST['serial_no'], FILTER_SANITIZE_STRING);
			$asset_verif_stat      = filter_var($_POST['asset_verif_stat'], FILTER_SANITIZE_STRING);
			$verif_by              = filter_var($_POST['verif_by'], FILTER_SANITIZE_STRING);
			$equipment_model       = filter_var($_POST['equipment_model'], FILTER_SANITIZE_STRING);
			$useful_life_month     = filter_var($_POST['useful_life_month'], FILTER_SANITIZE_STRING);
			$status                = filter_var($_POST['status'], FILTER_SANITIZE_STRING);
			$unit                  = filter_var($_POST['unit'], FILTER_SANITIZE_STRING);
			$territory             = filter_var($_POST['territory'], FILTER_SANITIZE_STRING);
			$region                = htmlspecialchars_decode($_POST['region'], ENT_QUOTES);
			$next_health_check_date  = filter_var($_POST['next_health_check_date'], FILTER_SANITIZE_STRING);
			$next_verif_date         = filter_var($_POST['next_verif_date'], FILTER_SANITIZE_STRING);

			$sql = "INSERT INTO [MME_ASSET] (
					Asset,
					Cost_Center,
					Inventory_Number,
					Asset_Description,
					Asset_Description_editable,
					Quantity,
					address1,
					address2,
					address3,
					address4,
					First_Acquired_On,
					Tagging_Date,
					Serial_Number,
					Asset_Verification_Status,
					Verification_By,
					Equipment_Model,
					Useful_Life_month,
					status,
					unit,
					territory,
					region,
					Health_Check_Date,
					Verif_Calib_Date
				)
				VALUES (
					'$asset_no',
					'$cost_center',
					'$inventory_name',
					'$asset_desc',
					'$asset_desc_editable',
					'$quantity',
					'$address1',
					'$address2',
					'$address3',
					'$address4',
					'$first_acq_date',
					'$tagg_date',
					'$serial_no',
					'$asset_verif_stat',
					'$verif_by',
					'$equipment_model',
					'$useful_life_month',
					'$status',
					'$unit',
					'$territory',
					'$region',
					'$next_health_check_date',
					'$next_verif_date'
				);
				";
			
		
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				die( print_r( sqlsrv_errors(), true));
				echo $result = json_encode(array('data' => 'FAILED'));	
			}else {
				echo $result = json_encode(array('data' => 'SUCCESS'));	
			}

}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
	}

?>