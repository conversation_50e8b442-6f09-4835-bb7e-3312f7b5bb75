<?php



header('Content-Type: application/json;charset=utf-8'); //json header
include 'connection.php';


$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	
	// Retrieve email owner test gear
	// Main datatable
		/*$sql = "SELECT *					
				FROM MME_ASSET";*/
		
		$sql = "SELECT * FROM FEEDBACK";
				
					
		$stmt = sqlsrv_query( $conn, $sql );
			
		if( $stmt === false) {
			echo $feedback = json_encode(array('data' => 'FAILED'));
		}
		
		if(sqlsrv_has_rows($stmt) != 1){ 
		  // echo "Invalid Username and Password.";
		  echo '{
					"sEcho": 1,
					"iTotalRecords": "0",
					"iTotalDisplayRecords": "0",
					"data": []
				}';    
		}
		else {
			$i = 1;
			while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
					$id                    = $i++;
					$region                = $row["region"];
					$comment               = $row["Comment"];
					$feedback              = $row["feedback"];					
					$status                = $row["status"];					
					$staff_id              = $row["Staff_id"];					
					$id_db                 = $row["id"];					
				
					
					$data[] = array($id, 
								$region,
								$comment,
								$feedback,
								$status, 
								$staff_id,
								$id_db);			
			}		
			echo $feedback = json_encode(array('data' => $data));
		}	

}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
	}

?>