<?php

// error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

$state       = filter_var($_POST['state'], FILTER_SANITIZE_STRING); 
$item        = filter_var($_POST['item'], FILTER_SANITIZE_STRING); 
$region      = filter_var($_POST['region'], FILTER_SANITIZE_STRING); 
$status      = filter_var($_POST['status'], FILTER_SANITIZE_STRING); 
	
if($state == "null"){
	$state = "";	
}

if($item == "null"){
	$item = "";	
}

if($region == "null"){
	$region = "";	
}
if($status == "null"){
	$status = "";	
}


if( $conn ) {	
		
		$sql = "SELECT DISTINCT MME_ASSET.*, USERS.department
				FROM MME_ASSET
				INNER JOIN USERS ON MME_ASSET.Verification_By=USERS.staff_id
				";		
		
			
		if($item == "" && $state == "" && $region== "" && $status == ""){
			$sql = $sql;
		} 
		else {
			$sql .= "WHERE ";
			$sql2 = "";
			
			if($item != ""){
				if($item == "ALL"){
					if($sql2 == ""){
						$sql2 .= "MME_ASSET.Asset_Description != ''";
					}
					else {
						$sql2 .= " AND MME_ASSET.Asset_Description != ''";
					}
				} else {
					if($sql2 == ""){
						$sql2 .= "MME_ASSET.Asset_Description = '$item'";
					}
					else{
						$sql2 .= " AND MME_ASSET.Asset_Description = '$item'";
					}
				}
			}
			
			if($state != "" ){
				if($sql2 == ""){
					$sql2 .= "MME_ASSET.address4 = '$state'";
				}
				else{
					$sql2 .= " AND MME_ASSET.address4 = '$state'";
				}
			}
			
			if($region != "" ){
				if($region == "ALL"){
					if($sql2 == ""){
						$sql2 .= "MME_ASSET.region != ''";
					}
					else {
						$sql2 .= " AND MME_ASSET.region != ''";
					}
				} else {
					if($sql2 == ""){
						$sql2 .= "MME_ASSET.region = '$region'";
					}
					else{
						$sql2 .= " AND MME_ASSET.region = '$region'";
					}
				}
			}
			
			if($status != "" ){
				if($status == "ALL"){
					if($sql2 == ""){
						$sql2 .= "MME_ASSET.status != ''";
					}
					else {
						$sql2 .= " AND MME_ASSET.status != ''";
					}
				} else {
					if($sql2 == ""){
						$sql2 .= "MME_ASSET.status = '$status'";
					}
					else{
						$sql2 .= " AND MME_ASSET.status = '$status'";
					}
				}
			}
			
			$sql .= $sql2;				
			
		}			
		//}
				
		//echo $sql;			
		$stmt = sqlsrv_query( $conn, $sql );
			
		if( $stmt === false) {
			die( print_r( sqlsrv_errors(), true) );		
		}
		
		if(sqlsrv_has_rows($stmt) != 1){ 
			   echo '{
					"sEcho": 1,
					"iTotalRecords": "0",
					"iTotalDisplayRecords": "0",
					"data": []
				}';
			}
		else {

			$i = 1;
			while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
					$id                   = $i++;
					$asset                = htmlspecialchars($row["Asset"]);
					$inventory_no         = htmlspecialchars($row["Inventory_Number"]);
					$cost_center          = htmlspecialchars(trim($row["Cost_Center"]));				
					$inventory_name       = htmlspecialchars($row["Asset_Description"])." ".htmlspecialchars($row["Asset_Description_editable"]);
					$serial_number        = htmlspecialchars($row["Serial_Number"]);
					$health_check_date    = date_format($row["Health_Check_Date"],'d-M-Y');
					if($health_check_date == "01-Jan-1900"){
						$health_check_date = "";
					}
					$verif_calib_date     = date_format($row["Verif_Calib_Date"],'d-M-Y');
					if($verif_calib_date == "01-Jan-1900"){
						$verif_calib_date = "";
					}
					$owner                = htmlspecialchars(trim($row["Verification_By"]));
					//echo $owner                = explode(" ", $row["Verification_By"]);
					if ($owner == null ) { $owner = "NULL"; } else { $owner = htmlspecialchars($row["Verification_By"]); } 
					$status               = htmlspecialchars($row["status"]);
					$division             = htmlspecialchars($row["department"]);
					if ($division == null ) { $division = "NULL"; } else { $division = htmlspecialchars($row["department"]); } 
					$location             = htmlspecialchars($row["address3"]).','. htmlspecialchars($row["address4"]);
					$sn                    = htmlspecialchars($row["Serial_Number"]); 
					
					
					$data[] = array($id, 
								$cost_center,
								$asset,
								$sn,
								$inventory_name, 
								$health_check_date,
								$verif_calib_date,
								$owner,
								$status,
								$location);
			}
			
			// $data = json_decode($json,true);
			echo $inventory = json_encode(array('data' => $data), JSON_UNESCAPED_UNICODE);

			
		}	

}
else{
		echo "Connection could not be established.<br />";
		// die( print_r( sqlsrv_errors(), true));
	}

	
	
?>