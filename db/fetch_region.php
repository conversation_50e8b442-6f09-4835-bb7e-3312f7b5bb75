<?php

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';


/*
$serverName = "172.20.49.37"; //serverName\instanceName
$connectionInfo = array( "Database"=>"COMME", "UID"=>"comme", "PWD"=>"%user456");*/
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	
			/*$staff_id = $_POST['staff_id'];*/
			$staff_id      = filter_var($_POST['staff_id'], FILTER_SANITIZE_STRING); 
			//$staff_id         = $_POST['staff_id'];
			
			
			$sql = "SELECT department,email FROM [USERS] WHERE staff_id ='".$staff_id."' ";
				
				$stmt = sqlsrv_query( $conn, $sql );
				
				if( $stmt === false) {
					//die( print_r( sqlsrv_errors(), true) );	
				 echo '{
						"sEcho": 1,
						"iTotalRecords": "0",
						"iTotalDisplayRecords": "0",
						"data": []
					}';					
				}
				else {
					while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
	
							$region      = $row["department"];
							$email_user  = $row['email'];


						$data[] = array("",$region,$email_user);
					}
						//$data[] = array("1",$_POST["full_name"],"3","4");
					echo $userdetails = json_encode(array('data' => $data));
				}
		
			

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
}

?>