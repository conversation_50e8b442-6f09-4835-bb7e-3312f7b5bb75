<?php



header('Content-Type: application/json;charset=utf-8'); //json header
include 'connection.php';


$conn = sqlsrv_connect( $serverName, $connectionInfo);


$vc             = filter_var($_POST['vc'], FILTER_SANITIZE_STRING); 
$item_mmme      = filter_var($_POST['item_mmme'], FILTER_SANITIZE_STRING); 



if($vc == "null"){
	$vc = "";	
}

if($item_mmme == "null"){
	$item_mmme = "";	
}




if( $conn ) {	
	// Retrieve email owner test gear
	// Main datatable
		
		
		$sql = "SELECT * FROM MME_MASTER_ASSET2 ";
		
			
		if($vc == "" && $item_mmme == "" ){
			$sql = $sql;
		} 
		else {
			$sql .= "WHERE ";
			$sql2 = "";
			
			if($vc != ""){
				if($vc == "ALL"){
					if($sql2 == ""){
						$sql2 .= "MME_MASTER_ASSET2.vc != ''";
					}
					else {
						$sql2 .= " AND MME_MASTER_ASSET2.vc != ''";
					}
				} else {
					if($sql2 == ""){
						$sql2 .= "MME_MASTER_ASSET2.vc = '".$vc."'";
					}
					else{
						$sql2 .= " AND MME_MASTER_ASSET2.vc = '".$vc."'";
					}
				}
			}
			
			if($item_mmme != "" ){
				if($item_mmme == "ALL"){
					if($sql2 == ""){
						$sql2 .= "MME_MASTER_ASSET2.master_mme_name != ''";
					}
					else {
						$sql2 .= " AND MME_MASTER_ASSET2.master_mme_name != ''";
					}
				} else {
					if($sql2 == ""){
						$sql2 .= "MME_MASTER_ASSET2.master_mme_name = '".$item_mmme."'";
					}
					else{
						$sql2 .= " AND MME_MASTER_ASSET2.master_mme_name = '".$item_mmme."'";
					}
				}
			}

			
			$sql .= $sql2;				
			
		}			
		
		$stmt = sqlsrv_query( $conn, $sql );
			
		if( $stmt === false) {
			//die( print_r( sqlsrv_errors(), true) );		
			echo $inventory = json_encode(array('data' => 'FAILED'));
		}
		
		if(sqlsrv_has_rows($stmt) != 1){ 
			   echo '{
					"sEcho": 1,
					"iTotalRecords": "0",
					"iTotalDisplayRecords": "0",
					"data": []
				}';
			}
		else {
			$i = 1;
			while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
				$id                              = $i++;
				$master_mme_asset_id             = htmlspecialchars($row["master_mme_asset_id"]);
				$vc                              = htmlspecialchars($row["vc"]);
				$vc_location                     = htmlspecialchars($row["vc_location"]);					
				$master_mme_name                 = htmlspecialchars($row["master_mme_name"]);	
				$serial_number                   = htmlspecialchars($row["serial_no"]);	
				$cost_center                     = htmlspecialchars($row["cost_center"]);
				$health_check_date    = date_format($row["health_check_date"],'d-M-Y');
				if($health_check_date == "01-Jan-1900"){
					$health_check_date = "";
				}
				$calib_date     = date_format($row["calibration_due_date"],'d-M-Y');
				if($calib_date == "01-Jan-1900"){
					$calib_date = "";
				}
				$owner                            = htmlspecialchars($row["staff_id"]);
				$status                           = htmlspecialchars($row["status"]);
										

					$data[] = array($id, 
						$vc,
						$cost_center,
						$master_mme_asset_id,
						$master_mme_name, 
						$serial_number,								
						$health_check_date,
						$calib_date,
						$status,
						$owner,									
						$vc_location);		
			}		
			echo $inventory = json_encode(array('data' => $data));
			
		}	

}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
	}

?>