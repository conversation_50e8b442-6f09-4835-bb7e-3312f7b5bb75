<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header
include 'connection.php';


$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {

			
			$staff_id        = filter_var($_POST['staff_id'], FILTER_SANITIZE_STRING); 
			$area_expertise  = filter_var($_POST['area_expertise'], FILTER_SANITIZE_STRING); 
			$region          = filter_var($_POST['region'], FILTER_SANITIZE_STRING); 
			$training_name   = filter_var($_POST['training_name'], FILTER_SANITIZE_STRING); 
			$status          = filter_var($_POST['status'], FILTER_SANITIZE_STRING); 
			
			
				$sql = "UPDATE [MME_VERIFIER_lab]
						SET
						Area_Expertise = '$area_expertise',
						Training_Name = '$training_name',
						region = '$region',
						status = '$status'
						WHERE Staff_id='$staff_id' AND Training_Name='$training_name'
				";

			
			
			/*$sql = "INSERT INTO [MME_VERIFIER_lab] ( Staff_id,Staff_Name,Area_Expertise,certificate,status, Training_Name) VALUES 
					('$staff_id','$staff_name','$area_expertise','$attachment','$status','$training_name')";
			*/
			
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				//die( print_r( sqlsrv_errors(), true));
				echo $result = json_encode(array('data' => 'FAILED'));	
			}
			else {
				echo $result = json_encode(array('data' => 'SUCCESS'));
			}

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
}

?>