<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header
include 'connection.php';


$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	

				$workgroup         = filter_var($_GET['workgroup'], FILTER_SANITIZE_STRING);	
				//$workgroup = 'SFC & BPQM';
				
				
				if($workgroup == "ADMIN"){	
				
					// storing  request (ie, get/post) global array to a variable  
					$requestData= $_REQUEST;
					
					$columns = array( 
					// datatable column index  => database column name
						0 =>'id', 
						1 => 'vc',
						2=> 'cost_center',
						3=> 'master_mme_asset_id',
						4=> 'master_mme_name',
						5=> 'manufacturer',
						6=> 'model',
						7=> 'user',
						8=> 'serial_no',
						9=> 'health_check_date',
						10=> 'calibration_due_date',
						11=> 'status',
						12=> 'remarks',
						13=> 'staff_id',
						14=> 'vc_location',
						14=> 'region'
					);
				
					// getting total number records without any search
					$sql = "SELECT * FROM MME_MASTER_ASSET2";
					$stmt = sqlsrv_query( $conn, $sql );
				
					$totalData = sqlsrv_has_rows($stmt);
					$totalFiltered = $totalData;  // when there is no search parameter then total number rows = total number filtered rows.

				
					$sql = "SELECT * FROM MME_MASTER_ASSET2 WHERE 1=1 ";
					// echo $sql;
					$stmt = sqlsrv_query( $conn, $sql );
					$totalFiltered = sqlsrv_has_rows($stmt); // when there is a search parameter then we have to modify total number filtered rows as per search result. 
					//$sql.=" ORDER BY ". $columns[$requestData['order'][0]['column']]."   ".$requestData['order'][0]['dir']."  LIMIT ".$requestData['start']." ,".$requestData['length']."   ";
					/* $requestData['order'][0]['column'] contains colmun index, $requestData['order'][0]['dir'] contains order such as asc/desc  */	
					//echo $sql;	
				
					if( !empty($requestData['search']['value']) ) {
						$sql.=" AND ( master_mme_asset_id LIKE '".$requestData['search']['value']."%' ";    
						$sql.=" OR cost_center LIKE '".$requestData['search']['value']."%' ";
						$sql.=" OR vc LIKE '".$requestData['search']['value']."%' )";
					}

					$sql.=" ORDER BY ". $columns[$requestData['order'][0]['column']]."   ".$requestData['order'][0]['dir']."  LIMIT ".$requestData['start']." ,".$requestData['length']."   ";

					$data = array();
					$query = "SELECT * FROM MME_MASTER_ASSET2 WHERE 1=1";

					//echo $query;
					$i=1;			
					while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
						
						//$a = $row["master_mme_asset_id"];
						$nestedData=array(); 
						$nestedData[] = $i;
						//$nestedData[] = $a;
						//$nestedData[] = "<button type='button' class='btn btn-sm btn-table' name='mme_asset_edit_btn' id='$a'>$a</button>";
						$nestedData[] = htmlspecialchars($row["vc"]);
						$nestedData[] = htmlspecialchars($row["cost_center"]);
						$nestedData[] = htmlspecialchars($row["master_mme_asset_id"]);
						$nestedData[] = htmlspecialchars($row["master_mme_name"]);
						$nestedData[] = htmlspecialchars($row["manufacturer"]);
						$nestedData[] = htmlspecialchars($row["model"]);
						$nestedData[] = htmlspecialchars($row["user"]);
						$nestedData[] = htmlspecialchars($row["serial_no"]);
						$nestedData[] = date_format($row["health_check_date"],'d-M-Y');
						$nestedData[] = date_format($row["calibration_due_date"],'d-M-Y');
						$nestedData[] = htmlspecialchars($row["status"]);
						$nestedData[] = htmlspecialchars($row["remarks"]);
						$nestedData[] = htmlspecialchars(trim($row["staff_id"]));
						$nestedData[] = htmlspecialchars((string)$row["vc_location"]);					
						$nestedData[] = htmlspecialchars((string)$row["region"]);					
						
						$data[] = $nestedData;
						
						$i++;
						//var_dump($data);
						
					} 	

					$json_data = array(
								"draw"            => intval( $requestData['draw'] ),   
								"recordsTotal"    => intval( $totalData ),  
								"recordsFiltered" => intval( $totalFiltered ), 
								"data"            => $data  
								);
			
					//echo $inventory = json_encode(array('data' => $data));
			
					echo json_encode($json_data);			
				
				} else {
				
					$sql = "SELECT * FROM MME_MASTER_ASSET2";
					
					
					
						
			
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false) {
				die( print_r( sqlsrv_errors(), true) );		
			}
			
			if(sqlsrv_has_rows($stmt) != 1){ 
			  // echo "Invalid Username and Password.";
			}
			else {
				$i=1;
				while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
						$id                   = $i;
						$vc                   = htmlspecialchars($row["vc"]);
						$cost_center          = htmlspecialchars($row["cost_center"]);
						$master_mme_asset_id  = htmlspecialchars($row["master_mme_asset_id"]);
						$master_mme_name      = htmlspecialchars($row["master_mme_name"]);
						$manufacturer         = htmlspecialchars($row["manufacturer"]);
						$model                = htmlspecialchars($row["model"]);
						$user                 = htmlspecialchars($row["user"]);
						$serial_no            = htmlspecialchars($row["serial_no"]);
						$health_check_date    = date_format($row["health_check_date"],'d-M-Y');
						$calibration_due_date = date_format($row["calibration_due_date"],'d-M-Y');
						$status               = htmlspecialchars($row["status"]);
						$remark               = htmlspecialchars($row["remarks"]);
						$staff_id             = htmlspecialchars($row["staff_id"]);
						$vc_location          = htmlspecialchars($row["vc_location"]);
						$region          = htmlspecialchars($row["region"]);

						$data[] = array($id, 
									$vc,
									$cost_center,
									$master_mme_asset_id, 
									$master_mme_name,
									$manufacturer,
									$model,
									$user,
									$serial_no,
									$health_check_date,
									$calibration_due_date,   
									$status,
									$remark,
									$staff_id,
									$vc_location,	
									$region);		

				$i++;
				}		
				echo $inventory = json_encode(array('data' => $data));
			}				
		}
		
}
else{
		echo "Connection could not be established.<br />";
		die( print_r( sqlsrv_errors(), true));
	}

?>