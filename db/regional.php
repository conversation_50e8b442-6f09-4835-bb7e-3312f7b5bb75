<?php

header('Content-Type: application/json;charset=utf-8'); //json header
include 'connection.php';
$function_logic = filter_var($_POST['function'], FILTER_SANITIZE_STRING);
session_start();

if ($function_logic == 'create')
{
	$conn = sqlsrv_connect( $serverName, $connectionInfo);

	if ($conn) {

		$regional_name = filter_var($_POST['regional_name'], FILTER_SANITIZE_STRING);
		$division_lob = filter_var($_POST['division_lob'], FILTER_SANITIZE_STRING);

		// Find in Database Before Add
		$sql = "SELECT * FROM [COMME].[dbo].[regional_list] WHERE regional_name = '$regional_name' AND division = '$division_lob'";
		$stmt = sqlsrv_query( $conn, $sql );
		if( $stmt === false) {
			echo json_encode(array('data' => 'failed', 'message'=> "statement failed"));
			die();
		}

		if (sqlsrv_has_rows($stmt)) {

			echo json_encode(array('data' => 'failed', 'message'=>'Data already exist'));

		} else {

			$staff_id = $_SESSION['staff_id'];

			$sql = "INSERT INTO [COMME].[dbo].[regional_list] (
				regional_name,
				division,
				created_by
			)
			VALUES (
				'$regional_name',
				'$division_lob',
				'$staff_id'
			)";


			$stmt = sqlsrv_query( $conn, $sql );

			if( $stmt === false ) {
				die( print_r( sqlsrv_errors(), true));
				echo $result = json_encode(array('data' => 'failed', 'message'=> 'failed created new data'));	
			}else {
				echo $result = json_encode(array('data' => 'success', 'message'=> 'Successfull created new data'));	
			}
		}


	} else {
		echo json_encode(array('data' => 'failed', 'message'=> "Connection could not be established"));
	}
}
else if ($function_logic == 'edit') 
{

	$staff_id = $_SESSION['staff_id'];
	$regional_name = $_POST['regional_name'];
	$division_lob = $_POST['division_lob'];
	$rowid = $_POST['rowid'];

	$conn = sqlsrv_connect( $serverName, $connectionInfo);

	if ($conn) {

		$sql = "UPDATE [regional_list] SET regional_name='$regional_name',division='$division_lob',updated_by='$staff_id',updated_at=getdate() WHERE regional_id=$rowid";

		$stmt = sqlsrv_query( $conn, $sql );

		if( $stmt === false ) {
		    echo $result = json_encode(array('data' => 'failed', 'message'=> "Updating execution failed. Error message [".sqlsrv_errors()[0]['message']."\n"));
		} else {
		    if( is_resource($stmt) ) {
		        // echo "Updating executed successfully.\n";
		        echo $result = json_encode(array('data' => 'success', 'message'=> 'Successfull update data'));
		        // You can fetch the results using sqlsrv_fetch_array() or other similar functions
		    } else {
		        echo $result = json_encode(array('data' => 'failed', 'message'=> "Updating execution failed. Error message [".sqlsrv_errors()[0]['message']."\n"));
		    }
		}

		// Close the connection and statement resources
		sqlsrv_free_stmt($stmt);
		sqlsrv_close($conn);

	} else {
		echo json_encode(array('data' => 'failed', 'message'=> "Connection could not be established"));
	}
}
else if ($function_logic == 'display') 
{
	$conn = sqlsrv_connect( $serverName, $connectionInfo);
	$sql = "SELECT * FROM [COMME].[dbo].[regional_list]";
	$stmt = sqlsrv_query( $conn, $sql );
	if(sqlsrv_has_rows($stmt) != 1){
	   echo '{
			"sEcho": 1,
			"iTotalRecords": "0",
			"iTotalDisplayRecords": "0",
			"regional_list_data": []
		}';
	} else {
		$i = 1;
		while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
			
			$regional_id = $row['regional_id'];
			$regional_name = $row['regional_name'];
			$division_lob = $row['division'];
			$created_by = $row['created_by'];
			$updated_by = $row['updated_by'];
			$created_at = date_format($row["created_at"],'d-M-Y');
			$updated_at = date_format($row["updated_at"],'d-M-Y');
			$action = "<button type='button' class='btn btn-sm btn-table btn-block mb-1' name='editFunction'>Edit</button> <button type='button' class='btn btn-sm btn-table btn-block mb-1' style='background-color:red' name='deleteFunction'>Delete</button>";

			$data[] = array(
				'checkbox'=>'',
				'col_row_number' => $i, 
				'col_regional_name' => $regional_name,
				'col_division_lob' => $division_lob,
				'col_created_by' => $created_by, 
				'col_updated_by' => $updated_by,								
				'col_created_at' => $created_at,
				'col_updated_at' => $updated_at,
				'col_action' => $action,
				'col_row_id' => $regional_id
			);

			$i++;
		};

		echo $inventory = json_encode(array('regional_list_data' => $data));
	}
}
else if ($function_logic == 'delete')
{
	$rowid = $_POST['rowid'];

	$conn = sqlsrv_connect( $serverName, $connectionInfo);

	if ($conn) {

		$sql = "DELETE FROM [regional_list] WHERE regional_id=$rowid";

		$stmt = sqlsrv_query( $conn, $sql );

		if( $stmt === false ) {
		    echo $result = json_encode(array('data' => 'failed', 'message'=> "Deleting execution failed. Error message [".sqlsrv_errors()[0]['message']."\n"));
		} else {
		    if( is_resource($stmt) ) {
		        // echo "Updating executed successfully.\n";
		        echo $result = json_encode(array('data' => 'success', 'message'=> 'Successfull deleting data'));
		        // You can fetch the results using sqlsrv_fetch_array() or other similar functions
		    } else {
		        echo $result = json_encode(array('data' => 'failed', 'message'=> "Deleting execution failed. Error message [".sqlsrv_errors()[0]['message']."\n"));
		    }
		}

		// Close the connection and statement resources
		sqlsrv_free_stmt($stmt);
		sqlsrv_close($conn);

	} else {
		echo json_encode(array('data' => 'failed', 'message'=> "Connection could not be established"));
	}
}