<?php



header('Content-Type: application/json;charset=utf-8'); //json header
include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	
			$asset_no = filter_var($_POST['asset_no'], FILTER_SANITIZE_STRING);
			$status   = filter_var($_POST['status'], FILTER_SANITIZE_STRING);

			$sql = "UPDATE [MME_ASSET]
					SET status='$status'
					WHERE Asset='".$asset_no."'";
		
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				//die( print_r( sqlsrv_errors(), true));
				echo $result = json_encode(array('data' => 'FAILED'));
			}
			else {
				echo $result = json_encode(array('data' => 'SUCCESS'));
			}
		
			

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
}

?>