<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	
			$asset_no       = filter_var($_POST['asset_no'], FILTER_SANITIZE_STRING);
			$datecalib      = filter_var($_POST['datecalib'], FILTER_SANITIZE_STRING);
			$update_stat    = filter_var($_POST['update_stat'], FILTER_SANITIZE_STRING);


			$sql = "UPDATE [MME_MASTER_ASSET2]
				SET calibration_due_date='$datecalib'
				WHERE master_mme_asset_id='$asset_no'";


			

			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				echo $result = json_encode(array('data' => 'FAILED'));
			}
			else {
				echo $result = json_encode(array('data' => 'SUCCESS'));
			}		
			

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
}

?>