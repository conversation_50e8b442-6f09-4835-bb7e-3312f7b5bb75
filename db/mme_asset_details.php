<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	
		if(!empty($_POST["mmeassetid"])) {	
		
				$mmeassetid   = filter_var($_POST['mmeassetid'], FILTER_SANITIZE_STRING);

				$sql = "SELECT * FROM [MME_ASSET] where Asset='$mmeassetid'";
				
				$stmt = sqlsrv_query( $conn, $sql );
				
				if( $stmt === false) {
					echo $inventory = json_encode(array('data' => 'FAILED'));
				}
				else {
					$id = 1;
					while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
						
							$id                = $id++;
							$asset             = $row["Asset"];
							$cost_center       = $row["Cost_Center"];
							$inventory_number  = $row["Inventory_Number"];
							$asset_description = $row["Asset_Description"];
							$asset_descirption_editable =  $row["Asset_Description_editable"];
							$quantity          =  $row["Quantity"];
							$acquired_date     = date_format($row["First_Acquired_On"],'d-M-Y');
							if($acquired_date == "01-Jan-1900"){
								$acquired_date = "";
							}
							$tagging_date      = date_format($row["Tagging_Date"],'d-M-Y');    
							if($tagging_date == "01-Jan-1900"){
								$tagging_date = "";
							}							
							$serial_number     = $row["Serial_Number"];
							$asset_verification_status = $row["Asset_Verification_Status"];
							$staff_id          = $row["Verification_By"];
							$equipment_model   = $row["Equipment_Model"];
							$useful_life_month = $row["Useful_Life_month"];			
							$address           = $row["address1"]." ".$row["address2"] ." ". $row["address3"] ." ". $row["address4"];						
							$remark            = $row["Remark"];	
							
						$data[] = array($id, $asset, $cost_center, $inventory_number, $asset_description, 
										$asset_descirption_editable, $quantity, $acquired_date,
										$tagging_date, $serial_number,$asset_verification_status, $asset_desc_edit2,
										$equipment_model, $useful_life_month, $status, 
										$address,$remark);						
					}		
					echo $result = json_encode(array(
						'data' => 'SUCCESS',
						'asset_id'=>$asset,
						'cost_center'=>$cost_center,
						'inventory_number'=>$inventory_number,
						'asset_description'=>$asset_description,
						'asset_descirption_editable'=>$asset_descirption_editable,
						'quantity'=>$quantity,
						'acquired_date'=>$acquired_date,
						'tagging_date'=>$tagging_date,
						'serial_number'=>$serial_number,
						'asset_verification_status'=>$asset_verification_status,
						'staff_id'=>$staff_id,
						'equipment_model'=>$equipment_model,		
						'useful_life_month'=>$useful_life_month,		
						'address'=>$address,		
						'remark'=>$remark
					));	
				}	
			}
			

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";

}

?>