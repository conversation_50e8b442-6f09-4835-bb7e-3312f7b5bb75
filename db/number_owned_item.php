<?php


header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	

			//$staff_id = $_POST["staff_id"];
			//sqlinjection
			//$staff_id   = $_POST['staff_id'];	
			$staff_id      = filter_var($_POST['staff_id'], FILTER_SANITIZE_STRING);
			
				$sql = "SELECT MME_ASSET.Asset
					FROM MME_ASSET
					WHERE MME_ASSET.Verification_By LIKE '%".$staff_id."%'";
			
				
				$params = array();
				$options =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
				$stmt = sqlsrv_query( $conn, $sql , $params, $options );


				$row_count = sqlsrv_num_rows( $stmt );
				   
				if ($row_count === false)
				   echo "Error in retrieving row count.";
				else
				   echo $row_count;
				
				

}
else{
		echo "Connection could not be established.<br />";
		die( print_r( sqlsrv_errors(), true));
	}

?>