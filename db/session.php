<?php



header('Content-Type: application/json;charset=utf-8'); //json header
session_start();

include 'connection.php';
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {

			date_default_timezone_set("Asia/Kuala_Lumpur");
			$staff_id     = filter_var($_POST['staff_id'], FILTER_SANITIZE_STRING);
			$fullname     = filter_var($_POST['fullname'], FILTER_SANITIZE_STRING);
			$password     = filter_var($_POST['password'], FILTER_SANITIZE_STRING);
			$token        = filter_var($_POST['token'], FILTER_SANITIZE_STRING);
			$date = date('Y-m-d H:i');
			
			
			
				$sql = "SELECT * FROM [USERS] WHERE staff_id = '".$staff_id."' ";
				
				$stmt = sqlsrv_query( $conn, $sql );
				if ($stmt) {
					$rows = sqlsrv_has_rows( $stmt );
					if ($rows === true){
						
						while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
						$_SESSION["fullname"] = $fullname;
						$_SESSION["staff_id"] = $staff_id;
						$_SESSION["password"] = $password;
						$_SESSION["usertoken"] = $token;
						$_SESSION["workgroup"] = $row['workgroup'];
						$last_logon            = date_format($row["last_logon"],'Y-m-d H:i');
						$status                = $row["status"];
						}

						$new_time = date("Y-m-d H:i", strtotime('+1 hours', strtotime($last_logon))); 

						if ($date > $new_time || $status == ""){
							$sql2 = "UPDATE [USERS]
								SET 
								last_logon ='$date',
								counter = '',
								status = ''
								WHERE staff_id='$staff_id'";
							
							$stmt2 = sqlsrv_query( $conn, $sql2 );
						
							if( $stmt2 === false ) {
								die( print_r( sqlsrv_errors(), true));
							}
							
							echo $post_data = json_encode(array('status' => 'succeed'));
						}else {
							echo $post_data = json_encode(array('status' => 'locked'));
						}

					}
				}

}
else{
		echo "Connection could not be established.<br />";
		die( print_r( sqlsrv_errors(), true));
	}

?>


