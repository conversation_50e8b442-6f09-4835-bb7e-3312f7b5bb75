<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header
include 'connection.php';

/*
$serverName = "************"; //serverName\instanceName
$connectionInfo = array( "Database"=>"COMME", "UID"=>"comme", "PWD"=>"%user456");*/
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	

	$sql = "SELECT * FROM [MME_EQP1]";
				
	$stmt = sqlsrv_query( $conn, $sql );
		
	while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
			$id                   = $row["id"];
			$mme_equipment        = $row["mme_equipment"];
			$vci         		  = $row["vci"];
			
			$data[] = array($mme_equipment,$vci);			
	}		
	echo $inventory = json_encode(array('data' => $data));
}	

?>