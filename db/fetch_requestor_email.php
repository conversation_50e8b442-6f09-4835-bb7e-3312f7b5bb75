<?php



header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	

			$staff_id      = filter_var($_POST['staff_id'], FILTER_SANITIZE_STRING); 
			$owner_id      = filter_var($_POST['owner_id'], FILTER_SANITIZE_STRING);
			

			$sql = "SELECT  (
						SELECT contact_number FROM [USERS] WHERE staff_id = '".$owner_id."' 
							) AS owner,
							(
						SELECT email FROM [USERS] WHERE staff_id ='".$staff_id."'
							) AS borrower";
				
				$stmt = sqlsrv_query( $conn, $sql );
				
				if( $stmt === false) {
					echo '{
						"sEcho": 1,
						"iTotalRecords": "0",
						"iTotalDisplayRecords": "0",
						"data": []
					}';
				}
				else {
					while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
	
							$email               = $row["borrower"];
							$contact_number      = $row["owner"];
							


					//	$data[] = array("",$email,$contact_number,$fullname,"");
					}
					//echo $userdetails = json_encode(array('data' => $data));
					echo $result = json_encode(array(
						'data' => 'SUCCESS',
						'email'=>$email,
						'contact_number'=>$contact_number
					));	
				}
		
			

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
}

?>