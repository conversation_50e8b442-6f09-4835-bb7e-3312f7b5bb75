<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header
include 'connection.php';


$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	
	$sql = "SELECT * FROM MME_VERIFIER_lab";
				
	$stmt = sqlsrv_query( $conn, $sql );
	
	if( $stmt === false) {
		die( print_r( sqlsrv_errors(), true) );		
	}
	else {
		$i = 1;
		while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
				$id                       = $i++;
				// $id                       = htmlspecialchars($row["Id"]);
				$staff_id                 = htmlspecialchars($row["Staff_id"]);
				$staff_name               = htmlspecialchars($row["Staff_Name"]);
				$area_expertise           = htmlspecialchars($row["Area_Expertise"]);
				$training_name            = htmlspecialchars($row["Training_Name"]);
				$certificate              = htmlspecialchars($row["certificate"]);
				if ($certificate == null ) { $certificate = "NULL"; } else { $certificate = htmlspecialchars($row["certificate"]); } 
				$status                   = htmlspecialchars($row["status"]);		
				$region                   = htmlspecialchars($row["region"]);		


			//$data[] = array("a"=>$id,"b"=>$staff_id,"c"=>$staff_name,"d"=>$area_expertise,"e"=>$training_name,"f"=>$certificate,"g"=>$status);
			$data[] = array($id,$staff_id,$staff_name,$area_expertise,$training_name,$certificate,$status,$region);
		//$id++;
		}
			//$data[] = array("1",$_POST["full_name"],"3","4");
		echo $userdetails = json_encode(array('data' => $data));
	}

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		die( print_r( sqlsrv_errors(), true));
}

?>