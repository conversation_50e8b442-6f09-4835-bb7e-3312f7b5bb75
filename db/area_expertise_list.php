<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';


$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	

	$sql = "SELECT DISTINCT Area_Expertise FROM [MME_VERIFIER_lab]";
				
	$stmt = sqlsrv_query( $conn, $sql );
	
	
	while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
			//$id                  = $row["id"];
			$area_expertise        = $row["Area_Expertise"];
			//$vci         		   = $row["vci"];
			
			$data[] = array('data' => $area_expertise);	
	
	}
    //$alldata[] = array('data' => "ALL");	
    $newdata[] = array_merge($data);	
	echo $inventory = json_encode($newdata);
}	

?>

