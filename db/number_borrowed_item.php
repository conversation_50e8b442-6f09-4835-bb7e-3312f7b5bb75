<?php



header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';


$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	

			//$staff_id = $_POST["staff_id"];
			//sqlinjection
			//$staff_id   = $_POST['staff_id'];
			$staff_id      = filter_var($_POST['staff_id'], FILTER_SANITIZE_STRING);			
			
				$sql = "SELECT USERS_LOG.Asset_No, MME_ASSET.Asset_Description, MME_ASSET.Asset_Description_editable, MME_ASSET.Serial_Number, MME_ASSET.Verification_By
						FROM USERS_LOG
						INNER JOIN MME_ASSET ON USERS_LOG.Asset_No=MME_ASSET.Asset
						WHERE USERS_LOG.Staff_id = '".$staff_id."' AND MME_ASSET.status != 'AVAILABLE' AND USERS_LOG.Return_date IS NULL";
			
				
				$params = array();
				$options =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
				$stmt = sqlsrv_query( $conn, $sql , $params, $options );


				$row_count = sqlsrv_num_rows( $stmt );
				   
				if ($row_count === false)
				   echo "Error in retrieving row count.";
				else
				   echo $row_count;
				
				

}
else{
		echo "Connection could not be established.<br />";
		die( print_r( sqlsrv_errors(), true));
	}

?>