<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

/*
$serverName = "172.20.49.37"; //serverName\instanceName
$connectionInfo = array( "Database"=>"COMME", "UID"=>"comme", "PWD"=>"%user456");*/
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	

	$sql = "SELECT DISTINCT region FROM [MME_ASSET]";
				
	$stmt = sqlsrv_query( $conn, $sql );
	
	
	while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
			//$id                   = $row["id"];
			$region        = $row["region"];
			//$vci         		  = $row["vci"];
			
			$data[] = array('data' => $region);	
	
	}
    $alldata[] = array('data' => "ALL");	
    $newdata[] = array_merge($alldata,$data);	
	echo $inventory = json_encode($newdata);
}	

?>

