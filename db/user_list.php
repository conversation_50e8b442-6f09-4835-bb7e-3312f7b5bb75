<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

/*
$serverName = "172.20.49.37"; //serverName\instanceName
$connectionInfo = array( "Database"=>"COMME", "UID"=>"comme", "PWD"=>"%user456");*/
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	
	// Retrieve email owner test gear
	// Main datatable
		/*$sql = "SELECT *					
				FROM MME_ASSET";*/
		
		$sql = "SELECT * FROM USERS";
				
					
		$stmt = sqlsrv_query( $conn, $sql );
			
		if( $stmt === false) {
			//die( print_r( sqlsrv_errors(), true) );		
		}
		
		if(sqlsrv_has_rows($stmt) != 1){ 
		  // echo "Invalid Username and Password.";
		}
		else {
			$i = 1;
			while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
					$id                 = $i++;
					$staff_id           = htmlspecialchars($row["staff_id"]);
					$fullname           = htmlspecialchars($row["fullname"]);
					$email              = htmlspecialchars($row["email"]);					
					$workgroup          = htmlspecialchars($row["workgroup"]);
					$department         = htmlspecialchars($row["department"]);
					$contact_number     = htmlspecialchars($row["contact_number"]);
					$designation        = htmlspecialchars($row["designation"]);
					$supervisor_name    = html_entity_decode($row["supervisor_name"]);
					$supervisor_email   = htmlspecialchars($row["supervisor_email"]);
					$time_stamp   = date_format($row["last_logon"],'Y-m-d H:i');
					if($time_stamp == false){
						$time_stamp = "";
					}
					
					$data[] = array($id, 
								$staff_id,
								$fullname,
								$email,
								$department, 
								$contact_number,
								$designation,
								$supervisor_name,
								//$supervisor_email,
								$workgroup,			
								$time_stamp);			
			}		
			echo $inventory = json_encode(array('data' => $data));
		}	

}
else{
		echo "Connection could not be established.<br />";
		die( print_r( sqlsrv_errors(), true));
	}

?>