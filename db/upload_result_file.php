<?php

$name     = $_FILES["file"]["name"];
$tmp_name = $_FILES['file']['tmp_name'];
$filename = filter_var($_POST['filename'], FILTER_SANITIZE_STRING); 
			
if (isset($_FILES["file"]["name"])) {

    if (!empty($name)) {
        $location = '../attachment/result/';

        if  (move_uploaded_file($tmp_name, $location.$filename)){
            $output = "SUCCESS";
        }
        else{
            $output = "FAIL UPLOAD".$tmp_name.$location.$filename;
        }
    }
    else {
        $output = "FILE UNAVAILBLE";
        
    }
}

echo $result = json_encode(array('data' => $output));


?>