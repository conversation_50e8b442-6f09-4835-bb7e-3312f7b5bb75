<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);
$today_date = date("Y-m-d");

if( $conn ) {
	
		
	$sql = 'SELECT * FROM MME_MASTER_ASSET2'; 		
			
	$stmt = sqlsrv_query( $conn, $sql );
	if( $stmt === false) {
		//die( print_r( sqlsrv_errors(), true) );		
	}else {
		while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
			$master_mme_asset_id                = htmlspecialchars($row["master_mme_asset_id"]);
			$calibration_due_date               = date_format($row["calibration_due_date"],'Y-m-d');
			$status                             = htmlspecialchars($row["status"]);

			if($calibration_due_date < $today_date && $status == "AVAILABLE"){

				$sql2 = "UPDATE [MME_MASTER_ASSET2]
						SET status='PENDING CALIBRATION'
						WHERE master_mme_asset_id='$master_mme_asset_id'";

				$stmt2 = sqlsrv_query( $conn, $sql2 );
				if( $stmt2 === false ) {
					die( print_r( sqlsrv_errors(), true));
				}
				sqlsrv_free_stmt( $stmt2);
				$data[] = array($master_mme_asset_id, 
						$calibration_due_date,
						$status);


			}
			// $data[] = array($asset, 
			// 			$verif_calib_date,
			// 			$status);

		}
		echo $inventory = json_encode(array('data' => $data));
	}
}
else{
		echo "Connection could not be established.<br />";

}

?>