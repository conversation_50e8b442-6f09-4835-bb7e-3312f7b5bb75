<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

/*
$serverName = "172.20.49.37"; //serverName\instanceName
$connectionInfo = array( "Database"=>"COMME", "UID"=>"comme", "PWD"=>"%user456");*/
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	
	$sql = "SELECT * FROM MME_VERIFIER_lab";
				
	$stmt = sqlsrv_query( $conn, $sql );
	
	if( $stmt === false) {
		die( print_r( sqlsrv_errors(), true) );		
	}
	else {
		$id = 1;
		while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
				$id                       = $id;
				$staff_id                 = $row["Staff_id"];
				$staff_name               = $row["Staff_Name"];
				$area_expertise           = $row["Area_Expertise"];
				$training_name            = $row["Training_Name"];
				$certificate              = $row["certificate"];
				if ($certificate == null ) { $certificate = "NULL"; } else { $certificate = $row["certificate"]; } 
				$status                   = $row["status"];		


			$data[] = array($staff_id,$staff_name,$area_expertise,$training_name,$certificate,$status);
		$id++;
		}
			//$data[] = array("1",$_POST["full_name"],"3","4");
		echo $userdetails = json_encode(array('data' => $data));
	}

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		die( print_r( sqlsrv_errors(), true));
}

?>