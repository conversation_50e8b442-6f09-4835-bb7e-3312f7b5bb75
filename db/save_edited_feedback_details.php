<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

/*
$serverName = "172.20.49.37"; //serverName\instanceName
$connectionInfo = array( "Database"=>"COMME", "UID"=>"comme", "PWD"=>"%user456");*/
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	
			/*$feedback                 = $_POST['feedback_edit']; 						
			$status                   = $_POST['status_edit']; 						
			$id                       = $_POST['id']; */
			
			$feedback   = filter_var($_POST['feedback_edit'], FILTER_SANITIZE_STRING);
			$status     = filter_var($_POST['status_edit'], FILTER_SANITIZE_STRING);
			$id         = filter_var($_POST['id'], FILTER_SANITIZE_STRING);


			
		
			$sql = "UPDATE [FEEDBACK]
					SET 
					feedback='$feedback',
					status='$status'		
					WHERE id='$id'";
			
		
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				echo $result = json_encode(array('data' => 'FAILED'));		
			}else {
				echo $result = json_encode(array('data' => 'SUCCESS'));		
			}
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
	}

?>