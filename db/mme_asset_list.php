<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header
include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	

		session_start();
		
		$staff_id = $_SESSION["staff_id"];
		
		$sql = "SELECT USERS.department
				FROM USERS
				WHERE USERS.staff_id = '$staff_id'";
					
		$stmt = sqlsrv_query( $conn, $sql );
			
		if( $stmt === false) {
			die( print_r( sqlsrv_errors(), true) );		
		}
		
		if(sqlsrv_has_rows($stmt) != 1){ 
		  // echo "Invalid Username and Password.";
		  echo '{
					"sEcho": 1,
					"iTotalRecords": "0",
					"iTotalDisplayRecords": "0",
					"data": []
				}';
		}
		else {
			$i = 1;
			while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
				$division                = $row["department"];
			}
		}
		
		
		$sql = "SELECT DISTINCT MME_ASSET.*, USERS.department
				FROM MME_ASSET
				INNER JOIN USERS ON MME_ASSET.Verification_By=USERS.staff_id WHERE USERS.department = '$division'";
					
		$stmt = sqlsrv_query( $conn, $sql );
			
		if( $stmt === false) {
			die( print_r( sqlsrv_errors(), true) );		
		}
		
		if(sqlsrv_has_rows($stmt) != 1){ 
			echo '{
				"sEcho": 1,
				"iTotalRecords": "0",
				"iTotalDisplayRecords": "0",
				"data": []
			}';   
		}
		else {
			$i = 1;
			while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
					$id                   = $i++;
					$asset                = htmlspecialchars($row["Asset"]);
					$inventory_no         = htmlspecialchars($row["Inventory_Number"]);
					$cost_center          = htmlspecialchars($row["Cost_Center"]);					
					$inventory_name       = htmlspecialchars($row["Asset_Description"]).", ".htmlspecialchars($row["Asset_Description_editable"]);
					$serial_number        = htmlspecialchars($row["Serial_Number"]);
					$health_check_date    = date_format($row["Health_Check_Date"],'d-M-Y');
					if($health_check_date == "01-Jan-1900"){
						$health_check_date = "";
					}
					$verif_calib_date     = date_format($row["Verif_Calib_Date"],'d-M-Y');
					if($verif_calib_date == "01-Jan-1900"){
						$verif_calib_date = "";
					}
					//$owner                = $row["Verification_By"];
					$owner                = explode(" ", htmlspecialchars($row["Verification_By"]));
					if ($owner == null ) { $owner = "NULL"; } else { $owner = htmlspecialchars($row["Verification_By"]); } 
					$status               = htmlspecialchars($row["status"]);
					$division             = htmlspecialchars($row["department"]);
					if ($division == null ) { $division = "NULL"; } else { $division = htmlspecialchars($row["department"]); } 
					$location             = htmlspecialchars($row["address3"]).','. htmlspecialchars($row["address4"]);
					$sn                   = htmlspecialchars($row["Serial_Number"]);
					
					$data[] = array($id, 
								$cost_center,
								$asset,
								$sn,
								$inventory_name, 
								$health_check_date,
								$verif_calib_date,
								$owner,
								$status,
								$location);			
			}		
			echo $inventory = json_encode(array('data' => $data));
		}	

}
else{
		echo "Connection could not be established.<br />";
		// die( print_r( sqlsrv_errors(), true));
	}

?>