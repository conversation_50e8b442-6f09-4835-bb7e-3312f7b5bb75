<?php



header('Content-Type: application/json;charset=utf-8'); //json header


include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

$expertise = filter_var($_POST['expertise'], FILTER_SANITIZE_STRING);
$region    = filter_var($_POST['region'], FILTER_SANITIZE_STRING);



if( $conn ) {	
				
		 
		if ($expertise == "ALL" && $region == "ALL"){
				$sql = "SELECT * FROM MME_VERIFIER_lab";
		}
		else if ($expertise == "" || $expertise == "ALL"){
			$sql = "SELECT * FROM MME_VERIFIER_lab";
		}
		else if ($region == "" || $region == "ALL"){
			$sql = "SELECT * FROM MME_VERIFIER_lab WHERE Area_Expertise='".$expertise."'";
		}
		else {
			$sql = "SELECT * FROM MME_VERIFIER_lab WHERE Area_Expertise='".$expertise."' AND region='".$region."'";
		}


		$stmt = sqlsrv_query( $conn, $sql);
			
		if( $stmt === false) {
			//die( print_r( sqlsrv_errors(), true) );
			echo $userdetails = json_encode(array('data' => 'FAILED'));			
		}

		if(sqlsrv_has_rows($stmt) != 1){ 
			   echo '{
					"sEcho": 1,
					"iTotalRecords": "0",
					"iTotalDisplayRecords": "0",
					"data": []
				}';
			}
		else {
			$i = 1;
			while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
				//$id                       = $id;
				$id                       = $i++;
				$staff_id                 = htmlspecialchars($row["Staff_id"]);
				$staff_name               = htmlspecialchars($row["Staff_Name"]);
				$area_expertise           = htmlspecialchars($row["Area_Expertise"]);
				$training_name            = htmlspecialchars($row["Training_Name"]);
				$certificate              = htmlspecialchars($row["certificate"]);
				if ($certificate == null ) { $certificate = "NULL"; } else { $certificate = $row["certificate"]; } 
				$status                   = htmlspecialchars($row["status"]);		
				$region                   = htmlspecialchars($row["region"]);		


			//$data[] = array("a"=>$id,"b"=>$staff_id,"c"=>$staff_name,"d"=>$area_expertise,"e"=>$training_name,"f"=>$certificate,"g"=>$status);
			$data[] = array($id,$staff_id,$staff_name,$area_expertise,$training_name,$certificate,$status,$region);
		
		}
			//$data[] = array("1",$_POST["full_name"],"3","4");
		echo $userdetails = json_encode(array('data' => $data));
	}	

}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
	}

?>