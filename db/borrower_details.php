<?php



header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	
				$mmeassetid  = filter_var($_POST['mmeassetid'], FILTER_SANITIZE_STRING);	
				
				$sql = "SELECT USERS_LOG.Staff_id, USERS_LOG.Purpose,USERS_LOG.Borrow_date,USERS_LOG.Return_date,
							[USERS].fullname,[USERS].contact_number,[USERS].email 
							FROM [USERS_LOG],[USERS]
							WHERE USERS_LOG.Staff_id = [USERS].staff_id AND USERS_LOG.Asset_No = '".$mmeassetid."' AND USERS_LOG.Return_date IS NULL
							ORDER BY logID ASC";
				$stmt = sqlsrv_query( $conn, $sql );
				
				if( $stmt === false) {
						//die( print_r( sqlsrv_errors(), true) );	
					echo $ownerdetail = json_encode(array('data' => 'FAILED'));							
				}
				else {
					while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
						
							$staff_id                = $row["Staff_id"];
							$fullname_loan           = $row["fullname"];
							$contact_number          = $row["contact_number"];
							$email                   = $row["email"];
							$borrow_date             = date_format($row["Borrow_date"],'d-M-Y');
							$retun_date              = date_format($row["Return_date"],'d-M-Y');
							$purpose                 = $row["Purpose"];
					
				
					}		
					echo $result = json_encode(array(
						'data' => 'SUCCESS',
						'staff_id'=>$staff_id,
						'fullname_loan'=>$fullname_loan,
						'contact_number'=>$contact_number,
						'email'=>$email,
						'borrow_date'=>$borrow_date,
						'return_date'=>$return_date,
						'purpose'=>$purpose	
					));	
	
				}

		sqlsrv_free_stmt( $stmt);
}
else{
		//echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
		echo $ownerdetail = json_encode(array('data' => 'FAILED'));	
}

?>