<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';


$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	

	// $sql = "SELECT DISTINCT region FROM [MME_ASSET]";
	$sql = "SELECT * FROM [regional_list]";
				
	$stmt = sqlsrv_query( $conn, $sql );
	
	
	while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
			//$id                   = $row["id"];
			// $region        = $row["region"];
			$region        = $row["regional_name"];
			//$vci         		  = $row["vci"];
			
			$data[] = array('data' => $region);	
	
	}
    // $alldata[] = array('data' => "ALL");	
	$newdata[] = array_merge($data);
	echo $inventory = json_encode($newdata);
}	

?>

