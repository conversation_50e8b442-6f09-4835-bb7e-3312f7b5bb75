<?php

//echo $_FILES['file']['name'];

include 'connection.php';
$conn = sqlsrv_connect( $serverName, $connectionInfo);

include "../plugin/simplexlsx/SimpleXLSX.php";

//$xlsx = new SimpleXLSX( $_FILES['file']['tmp_name'] );
$filename=$_FILES["file"]["tmp_name"];
// echo $xlsx;

if ($conn){	
  
	if ( $xlsx = SimpleXLSX::parse($filename) ) {
	  $j = 0;  
	  foreach ($xlsx->rows() as $data) {
		$j++;
		if ( $j == 1){
			continue;
		} else {
			$vc                    = filter_var($data[0], FILTER_SANITIZE_STRING);
			$vc_location           = filter_var($data[1], FILTER_SANITIZE_STRING);
			$cost_center           = filter_var($data[2], FILTER_SANITIZE_STRING);
			$master_mme_asset_id   = filter_var(trim($data[3]), FILTER_SANITIZE_STRING);
			$master_mme_name       = filter_var($data[4], FILTER_SANITIZE_STRING);
			$manufacturer          = filter_var($data[5], FILTER_SANITIZE_STRING);
			$model                 = filter_var($data[6], FILTER_SANITIZE_STRING);
			$serial_no             = filter_var($data[7], FILTER_SANITIZE_STRING);
			$user                  = filter_var($data[8], FILTER_SANITIZE_STRING);
			$health_check_date     = filter_var($data[9], FILTER_SANITIZE_STRING);
			$calibration_due_date  = filter_var($data[10], FILTER_SANITIZE_STRING);
			$staff_id              = filter_var($data[11], FILTER_SANITIZE_STRING);
			$remarks               = filter_var($data[12], FILTER_SANITIZE_STRING);
			$status                = filter_var($data[13], FILTER_SANITIZE_STRING);
			$region                = filter_var($data[14], FILTER_SANITIZE_STRING);
				
			// for ($i=0; $i<23;$i++){
			// 	echo $data[$i];
			// }

			$sql = "SELECT [MME_MASTER_ASSET2].master_mme_asset_id FROM [MME_MASTER_ASSET2] WHERE master_mme_asset_id ='$master_mme_asset_id'";
			$stmt = sqlsrv_query( $conn, $sql );
			if ($stmt) {					
				$rows = sqlsrv_has_rows( $stmt );	
				if ($rows === true){
					$sql2 = "UPDATE [MME_MASTER_ASSET2]
							SET 
							vc='$vc',
							vc_location='$vc_location',
							cost_center='$cost_center',
							master_mme_name='$master_mme_name',
							manufacturer='$manufacturer',
							model='$model',
							serial_no='$serial_no',
							[user]='$user',
							health_check_date='$health_check_date',
							calibration_due_date='$calibration_due_date',
							staff_id='$staff_id',
							remarks='$remarks',
							status='$status',							
							region='$region'								
							WHERE master_mme_asset_id='$master_mme_asset_id'";
				}
				else{
					$sql2 = "INSERT INTO [MME_MASTER_ASSET2] (
												master_mme_asset_id,
												master_mme_name,
												vc,
												vc_location,
												cost_center,
												manufacturer,
												model,
												serial_no,
												[user],
												health_check_date,
												calibration_due_date,
												staff_id,
												remarks,
												status,
												region
												) VALUES 
											  ('$master_mme_asset_id',
											  '$master_mme_name',
											  '$vc',
											  '$vc_location',
											  '$cost_center',
											  '$manufacturer',
											  '$model',
											  '$serial_no',
											  '$user',
											  '$health_check_date',
											  '$calibration_due_date',
											  '$staff_id',
											  '$remarks',
											  '$status',												  
											  '$region')";												  
					
				}
				$stmt2 = sqlsrv_query( $conn, $sql2 );
				if( $stmt2 === false ) {						
					die( print_r( sqlsrv_errors(), true));
					$result = 'FAILED';
				}else{
					$result = 'SUCCESS';	
				}				
				//throws a message if data successfully imported to mysql database from excel file
				sqlsrv_free_stmt( $stmt);
				sqlsrv_free_stmt( $stmt2);
			}else {
				//die( print_r( sqlsrv_errors(), true));
				$result = 'FAILED';
			}
						
		}
	  }
	  echo $result;

	} else {
	  echo SimpleXLSX::parseError();
	  echo 'FAILED';
	}
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
	}


?>