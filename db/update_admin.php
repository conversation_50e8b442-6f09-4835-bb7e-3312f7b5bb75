<?php



header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';


/*
$serverName = "172.20.49.37"; //serverName\instanceName
$connectionInfo = array( "Database"=>"COMME", "UID"=>"comme", "PWD"=>"%user456");*/

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	
			/*$staff_id                 = $_POST['staff_id']; 						
			$admin                    = $_POST['admin']; 	*/

			$staff_id     = filter_var($_POST['staff_id'], FILTER_SANITIZE_STRING);
			//sqlinjection
			//$staff_id     = $_POST['staff_id'];
			$admin        = filter_var($_POST['admin'], FILTER_SANITIZE_STRING);			
		
			
		
			$sql = "UPDATE [USERS]
					SET 
					workgroup='$admin'			
					WHERE staff_id='".$staff_id."'";
			
		
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				die( print_r( sqlsrv_errors(), true));
			}
			echo $result = json_encode(array('data' => 'SUCCESS'));		

}
else{
		echo "Connection could not be established.<br />";
		die( print_r( sqlsrv_errors(), true));
	}

?>