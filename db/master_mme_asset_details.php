<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header
include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
			$master_mmeassetid     = filter_var($_POST['master_mmeassetid'], FILTER_SANITIZE_STRING); 

			$sql = "SELECT * FROM [MME_MASTER_ASSET2] where master_mme_asset_id='$master_mmeassetid'";

			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				die( print_r( sqlsrv_errors(), true));
			}
			else {
				$id = 1;
				while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
					$id                    = $id++;
					$master_mme_name       = $row["master_mme_name"];
					$cost_center           = $row["cost_center"];
					$vc                    = $row["vc"];
					$vc_location           =  $row["vc_location"];
					$calibration_due_date     = date_format($row["calibration_due_date"],'d-M-Y');
					if($calibration_due_date == "01-Jan-1900"){
						$calibration_due_date = "";
					}
					$calibrator            =  $row["calibrator"];
					$manufacturer          =  $row["manufacturer"];
					$model                 =  $row["model"];
					$serial_no             =  $row["serial_no"];
					$user                  =  $row["user"];
					$remarks               =  $row["remarks"];
					$staff_id              =  $row["staff_id"];
					$region              =  $row["region"];
					
				}

				echo $result = json_encode(array(
					'data' => 'SUCCESS',
					'master_mme_name'=>$master_mme_name,
					'cost_center'=>$cost_center,
					'vc'=>$vc,
					'vc_location'=>$vc_location,
					'calibration_due_date'=>$calibration_due_date,
					'calibrator'=>$calibrator,
					'manufacturer'=>$manufacturer,
					'model'=>$model,
					'serial_no'=>$serial_no,
					'user'=>$user,
					'staff_id'=>$staff_id,
					'region'=>$region,
					'remarks'=>$remarks		
				));	

				
			}			

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		// die( print_r( sqlsrv_errors(), true));
}

?>