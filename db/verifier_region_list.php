<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	

	$sql = "SELECT DISTINCT region FROM [MME_VERIFIER_lab]";
				
	$stmt = sqlsrv_query( $conn, $sql );
	
	
	while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
			$region        = htmlspecialchars_decode($row["region"],ENT_QUOTES);
			
			$data[] = array('data' => $region);	
	
	}
    $alldata[] = array('data' => "ALL");	
    $newdata[] = array_merge($alldata,$data);	
	echo $inventory = json_encode($newdata);
}	

?>

