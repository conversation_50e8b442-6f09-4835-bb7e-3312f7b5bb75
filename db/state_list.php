<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

/*
$serverName = "************"; //serverName\instanceName
$connectionInfo = array( "Database"=>"COMME", "UID"=>"comme", "PWD"=>"%user456");*/
$conn = sqlsrv_connect( $serverName, $connectionInfo);

$region = $_GET["region"];

if( $conn ) {	

	$sql = "SELECT DISTINCT address4 FROM [MME_ASSET] WHERE region = '$region' ";
				
	$stmt = sqlsrv_query( $conn, $sql );
		
	while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
			//$id                   = $row["id"];
			$address4        = $row["address4"];
			//$vci         		  = $row["vci"];
			
			$data[] = array('data' => $address4);			
	}		
	echo $inventory = json_encode($data);
}	

?>

