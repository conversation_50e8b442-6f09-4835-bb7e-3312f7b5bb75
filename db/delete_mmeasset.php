<?php



header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	
	
	$asset_no      = filter_var($_POST['mme_asset_no'], FILTER_SANITIZE_STRING); 
	//sqlinjection
	//$asset_no            = $_POST['mme_asset_no'];
	//$asset_no            = $_POST['mme_asset_no']; 	
		
	$sql = "DELETE FROM [MME_ASSET]
			WHERE Asset='".$asset_no."'";
	
	$stmt = sqlsrv_query( $conn, $sql );
	
	if( $stmt === false ) {
		//die( print_r( sqlsrv_errors(), true));
		echo $result = json_encode(array('data' => 'FAILED'));	
	} else {
		echo $result = json_encode(array('data' => 'SUCCESS'));	
	}

}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
	}

?>