<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header
include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	
		
						
			// $sql = "SELECT DISTINCT MME_MASTER_ASSET2.*, USERS.workgroup
			// 		FROM MME_MASTER_ASSET2
			// 		INNER JOIN USERS ON MME_MASTER_ASSET2.staff_id=USERS.staff_id WHERE USERS.workgroup != '' AND MME_MASTER_ASSET2.status = 'SPARE'";

			$sql = "SELECT TB1.cost_center, TB1.region, TB1.master_mme_asset_id, TB1.serial_no, TB1.master_mme_name, TB1.manufacturer,TB1.model,
				TB1.staff_id,TB1.status,TB1.vc_location,TB1.vc, TB2.remarks, TB2.id
				FROM MME_MASTER_ASSET2 TB1 
				CROSS APPLY (
				SELECT DISTINCT TOP 1 TB2.* FROM MME_HEALTH_CHECK TB2 WHERE TB2.Asset = TB1.master_mme_asset_id AND TB1.status = 'SPARE'
				ORDER BY TB2.id DESC 
				) TB2;";
								
		$stmt = sqlsrv_query( $conn, $sql );
			
		if( $stmt === false) {
			die( print_r( sqlsrv_errors(), true) );		
		}
		
		if(sqlsrv_has_rows($stmt) != 1){ 
			echo '{
				"sEcho": 1,
				"iTotalRecords": "0",
				"iTotalDisplayRecords": "0",
				"data": []
			}';   
		}
		else {
			$i = 1;
			while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
					$id                   = $i++;
					$cost_center          = $row["cost_center"];
					$region               = $row["region"];
					$asset                = $row["master_mme_asset_id"];
					$sn                   = $row["serial_no"];										
					$inventory_name       = $row["master_mme_name"];
					$equipment_model      = $row["manufacturer"]." ".$row["model"] ;
					$owner                = trim($row["staff_id"]);
					if ($owner == null ) { $owner = "NULL"; } else { $owner = $row["staff_id"]; } 
					$status               = $row["status"];
					$location             = $row["vc_location"];
					$vc             	  = $row["vc"];
					$remarks             = $row["remarks"];
					
										
					$data[] = array($id, 
								$vc,
								$cost_center,
								//$region,
								$asset,
								$sn,
								$inventory_name, 
								$equipment_model,
								$owner,
								$status,	
								$remarks);			
			}		
			echo $inventory = json_encode(array('data' => $data));
		}	

}
else{
		echo "Connection could not be established.<br />";
		// die( print_r( sqlsrv_errors(), true));
	}

?>