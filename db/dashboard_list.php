<?php



header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);
$today_date =  date('Y-m-d');
if( $conn ) {
	
			$action  = filter_var($_GET['action'], FILTER_SANITIZE_STRING);

			
			if($action == "FAULTY"){
				$sql = " SELECT * FROM MME_ASSET WHERE status = 'FAULTY' ";
			}
			if($action == "SPARE"){
				$sql = " SELECT * FROM MME_ASSET WHERE status = 'SPARE' ";
			}
			if($action == "OBSOLETE"){
				$sql = " SELECT * FROM MME_ASSET WHERE status = 'OBSOLETE' ";
			}
			if($action == "BER"){
				$sql = " SELECT * FROM MME_ASSET WHERE status = 'BER' ";
			}
			if($action == "hc"){
				$sql = "  SELECT * FROM MME_ASSET WHERE Health_Check_Date <= '$today_date'  AND status = 'AVAILABLE'";
			}
			if($action == "verif"){
				$sql = "  SELECT * FROM MME_ASSET WHERE Verif_Calib_Date <= '$today_date'";
				$sql = "  SELECT * FROM MME_ASSET WHERE status = 'PENDING VERIFICATION'";
			}
			if($action == "inprog_verif"){
				$sql = " SELECT * FROM MME_ASSET WHERE status = 'IN PROGRESS VERIFICATION'";
			}
			if($action == "inprog_calib"){
				$sql = " SELECT * FROM MME_ASSET WHERE status = 'IN PROGRESS CALIBRATION'";
			}
			if($action == "confirmation"){
				$sql = " SELECT * FROM MME_ASSET WHERE status = 'CONFIRMATION'";
			}

			/////////MASTER TEST GEAR///////////

			if($action == "faulty_mtg"){
				$sql = " SELECT * FROM MME_MASTER_ASSET2 WHERE status = 'FAULTY' ";
			}
			if($action == "spare_mtg"){
				$sql = " SELECT * FROM MME_MASTER_ASSET2 WHERE status = 'SPARE' ";
			}
			if($action == "obsolete_mtg"){
				$sql = " SELECT * FROM MME_MASTER_ASSET2 WHERE status = 'OBSOLETE' ";
			}
			if($action == "ber_mtg"){
				$sql = " SELECT * FROM MME_MASTER_ASSET2 WHERE status = 'BER' ";
			}
			if($action == "hc_mtg"){
				$sql = "  SELECT * FROM MME_MASTER_ASSET2 WHERE health_check_date <= '$today_date'  AND status = 'AVAILABLE'";
			}
			if($action == "mtg_calib"){
				$sql = "  SELECT * FROM MME_MASTER_ASSET2 WHERE calibration_due_date <= '$today_date'";
			}
			if($action == "mtg_inprog_calib"){
				$sql = "  SELECT * FROM MME_MASTER_ASSET2 WHERE status = 'IN PROGRESS CALIBRATION'";
			}
			if($action == "mtg_confirmation"){
				$sql = "  SELECT * FROM MME_MASTER_ASSET2 WHERE status = 'CONFIRMATION'";
			}
			$stmt = sqlsrv_query( $conn, $sql );
			// echo $action;
			// echo $sql;
			// echo sqlsrv_has_rows($stmt);
			if( $stmt === false) {
				//die( print_r( sqlsrv_errors(), true) );	
				echo $inventory = json_encode(array('data' => 'FAILED'));
			}
			
			if(sqlsrv_has_rows($stmt) != 1){ 
			   echo '{
					"sEcho": 1,
					"iTotalRecords": "0",
					"iTotalDisplayRecords": "0",
					"data": []
				}';
			}
			else {
				$id = 1;
				while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {	
						if($action == "FAULTY"|| $action == "SPARE" || $action == "OBSOLETE" || $action == "BER" || $action == "hc" || $action == "verif" ||  $action == "inprog_verif" || $action == "inprog_calib" || $action == "confirmation" ){
							$log_id                  = $id++;
							$cost_center             = htmlspecialchars($row["Cost_Center"]);
							$Asset                   = htmlspecialchars($row["Asset"]);						
							$serial_no               = htmlspecialchars($row["Serial_Number"]);
							$Inventory_Name          = htmlspecialchars($row["Asset_Description"]." ".$row["Asset_Description_editable"]);
							$Verification_By         = htmlspecialchars($row["Verification_By"]);
							$region                  = htmlspecialchars($row["region"]);
							$hc_date                 = date_format($row["Health_Check_Date"],'d-M-Y');
							$verif_calib_date        = date_format($row["Verif_Calib_Date"],'d-M-Y'); 

							if($action == "hc" ){
								$data[] = array( 
									$log_id,
									$cost_center,
									$Asset,
									$serial_no, 
									$Inventory_Name,
									$hc_date,
									$Verification_By,
									$region);
								
						
							}
							else if($action == "verif" ||  $action == "inprog_verif" || $action == "inprog_calib"){
								$data[] = array( 
									$log_id,
									$cost_center,
									$Asset,
									$serial_no, 
									$Inventory_Name,
									$verif_calib_date,
									$Verification_By,
									$region);
							}
							else {
								$data[] = array( 
									$log_id,
									$cost_center,
									$Asset,
									$serial_no, 
									$Inventory_Name,
									$Verification_By,
									$region);	
							}
						}					
						
						
						//MASTER
						if($action == "hc_mtg" || $action == "mtg_calib" || $action == "mtg_inprog_calib" || $action == "mtg_confirmation" || $action == "faulty_mtg"  || $action == "spare_mtg" || $action == "obsolete_mtg" || $action == "ber_mtg"){
							
							$log_id                  = $id++;
							$master_cost_center      = htmlspecialchars($row["cost_center"]);
							$master_asset            = htmlspecialchars($row["master_mme_asset_id"]);
							$master_serial_no        = htmlspecialchars($row["serial_no"]);
							$master_tg_name          = htmlspecialchars($row["master_mme_name"]);
							$master_hc_date          = date_format($row["health_check_date"],'d-M-Y');
							$master_calib_date       = date_format($row["calibration_due_date"],'d-M-Y');
							$staff_id                = htmlspecialchars($row["staff_id"]);
							$vc                      = htmlspecialchars($row["vc"]);
							$region                  = htmlspecialchars($row["region"]);

							if($action == "hc_mtg" ){
								$data[] = array( 
									$log_id,
									$master_cost_center,
									$master_asset,
									$master_serial_no, 
									$master_tg_name,
									$master_hc_date,
									$staff_id,
									$region,
									$vc);
							}
							else if ($action == "mtg_calib" || $action == "mtg_inprog_calib"){
								$data[] = array( 
									$log_id,
									$master_cost_center,
									$master_asset,
									$master_serial_no, 
									$master_tg_name,
									$master_calib_date,
									$staff_id,
									$region,
									$vc);
							}else {
								$data[] = array( 
									$log_id,
									$master_cost_center,
									$master_asset,
									$master_serial_no, 
									$master_tg_name,
									$staff_id,
									$region,
									$vc);
							}
						}
						


		
				}		
				//var_dump($data);
				echo $inventory = json_encode(array('data' => $data));
			}					
		

		sqlsrv_free_stmt( $stmt);
}
else{
		//echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
		echo $inventory = json_encode(array('data' => 'FAILED'));	
}

?>