<?php



header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	

	$staff_id       = filter_var($_POST['staff_id'], FILTER_SANITIZE_STRING);
	$email_borrower = filter_var($_POST['email_borrower'], FILTER_SANITIZE_STRING);

	$sql = "UPDATE [USERS] Set email = '$email_borrower' WHERE staff_id = '".$staff_id."'";
	
	$stmt = sqlsrv_query( $conn, $sql );
	
	if( $stmt === false ) {
		echo $result = json_encode(array('data' => 'FAILED'));	
	}else{
		echo $result = json_encode(array('data' => 'SUCCESS'));	
	}
}
else{
		echo $result = json_encode(array('data' => 'FAILED'));	
	}

?>