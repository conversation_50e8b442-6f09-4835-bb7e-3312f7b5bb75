<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';


$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	
			$staff_id         = filter_var($_POST['staff_id'], FILTER_SANITIZE_STRING);
			$staff_name       = filter_var($_POST['staff_name'], FILTER_SANITIZE_STRING);
			$region           = filter_var($_POST['region'], FILTER_SANITIZE_STRING);
			$area_expertise   = filter_var($_POST['area_expertise'], FILTER_SANITIZE_STRING);
			$training_name    = filter_var($_POST['training_name'], FILTER_SANITIZE_STRING);
			$attachment       = filter_var($_POST['attachment'], FILTER_SANITIZE_STRING);
			$status           = filter_var($_POST['status'], FILTER_SANITIZE_STRING);
			
			$staff_id        = utf8_decode($staff_id);
			$staff_name      = utf8_decode($staff_name);
			$region          = utf8_decode($region);
			$area_expertise  = utf8_decode($area_expertise);
			$training_name   = utf8_decode($training_name);
			$attachment      = utf8_decode($attachment);
			$status          = utf8_decode($status);
			
				
		/*	$staff_id        = $_POST['staff_id'];
			$staff_name      = $_POST['staff_name'];
			$area_expertise  = $_POST['area_expertise'];
			$training_name   = $_POST['training_name'];
			$attachment      = $_POST['attachment'];
			$status          = $_POST['status'];*/
			
			
			$sql = "INSERT INTO [MME_VERIFIER_lab] ( Staff_id,Staff_Name,Area_Expertise,certificate,status, Training_Name,region) VALUES 
					('$staff_id','$staff_name','$area_expertise','$attachment','$status','$training_name','$region')";
			
				$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				die( print_r( sqlsrv_errors(), true));
				echo $result = json_encode(array('data' => 'FAILED'));	
			}
			else{
				echo $result = json_encode(array('data' => 'SUCCESS'));
			}

		sqlsrv_free_stmt( $stmt);
}
else{

		echo $result = json_encode(array('data' => 'FAILED'));	
}

?>