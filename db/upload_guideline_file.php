


<?php


$name     = $_FILES["file"]["name"];

if (isset($_FILES["file"]["name"])) {

	$name     = $_FILES["file"]["name"];
	$tmp_name = $_FILES['file']['tmp_name'];
	$error    = $_FILES['file']['error'];
	
	if (!empty($name)) {
		$location = '../attachment/guideline/';

		if  (move_uploaded_file($tmp_name, $location.$name)){
			$result = "SUCCESS";
		}
		else{

			$result = "FAIL UPLOAD";
		}

	} else {
		$result = "FAILED";
	}
}
echo $result;

?>