<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
			$gl_reference         = filter_var($_POST['gl_reference'], FILTER_SANITIZE_STRING);	
			$test_equipment_type  = filter_var($_POST['test_equipment_type'], FILTER_SANITIZE_STRING);	
			$guidelines_type      = filter_var($_POST['guidelines_type'], FILTER_SANITIZE_STRING);	
			$doc_owner            = filter_var($_POST['doc_owner'], FILTER_SANITIZE_STRING);	
			$initial_date         = filter_var($_POST['initial_date'], FILTER_SANITIZE_STRING);	
			$revised_date         = filter_var($_POST['revised_date'], FILTER_SANITIZE_STRING);	
			$attachment           = filter_var($_POST['attachment'], FILTER_SANITIZE_STRING);	
					
			$sql = "INSERT INTO [VERIFICATION_GUIDELINE] (gl_ref,test_equipment_type,guidelines_type,doc_owner, initial_date, revised_date,doc_link ) VALUES 
			('$gl_reference','$test_equipment_type','$guidelines_type','$doc_owner','$initial_date','$revised_date','$attachment')";
	
					
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				die( print_r( sqlsrv_errors(), true));
			}
			echo $result = json_encode(array('data' => 'SUCCESS'));

}
else{
		echo "Connection could not be established.<br />";
		die( print_r( sqlsrv_errors(), true));
	}

?>