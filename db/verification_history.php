<?php



header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	
			
			$mme_assetid      = filter_var($_GET['mme_assetid'], FILTER_SANITIZE_STRING); 

			
			$sql = "SELECT MME_VERIFICATION_CALIBRATION.id, MME_VERIFICATION_CALIBRATION.Asset, MME_VERIFICATION_CALIBRATION.Inventory_Name, 
					MME_VERIFICATION_CALIBRATION.Verification_date,MME_VERIFICATION_CALIBRATION.Verification_duedate, MME_VERIFICATION_CALIBRATION.Verification_interval,MME_VERIFICATION_CALIBRATION.aros_id,
					MME_VERIFICATION_CALIBRATION.justification,MME_VERIFICATION_CALIBRATION.mmecertnum,MME_VERIFICATION_CALIBRATION.status_activity, MME_VERIFICATION_CALIBRATION.calibrator,
					[USERS].fullname
					FROM MME_VERIFICATION_CALIBRATION,[USERS]
					WHERE MME_VERIFICATION_CALIBRATION.performed_by = [USERS].staff_id AND MME_VERIFICATION_CALIBRATION.Asset = '".$mme_assetid."' AND MME_VERIFICATION_CALIBRATION.activity LIKE '%VERIFICATION%' 
					ORDER BY id DESC";
			
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false) {
				//die( print_r( sqlsrv_errors(), true) );		
				echo $inventory = json_encode(array('data' => 'FAILED'));
			}
			
			if(sqlsrv_has_rows($stmt) != 1){ 
			   echo '{
					"sEcho": 1,
					"iTotalRecords": "0",
					"iTotalDisplayRecords": "0",
					"data": []
				}';
			}
			else {
				$id = 1;
				while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {						
						$log_id                  = $id++;
						$Asset                   = $row["Asset"];
						$Inventory_Name          = $row["Inventory_Name"];
						if($row["Verification_date"] == ""){
							$verif_date   = "";						
						}else {
							$verif_date              = date_format($row["Verification_date"],'d-M-Y');
						}

						if($row["Verification_duedate"] == ""){
							$verif_due_date   = "";						
						}else {
							$verif_due_date        = date_format($row["Verification_duedate"],'d-M-Y');
						}$verif_int               = $row["Verification_interval"];
						$justification           = $row["justification"];	
						if($row["aros_id"] != ""){
							$justification           = $row["justification"]."AROS ID:".$row["aros_id"];
						}						
						$mmecertnum                = $row["mmecertnum"];
						$verifier                = $row["calibrator"];
						//$remarks                 = $row["remarks"];
						//$performed_date          = date_format($row["performed_date"],'d-M-Y');
						$staff_name              = $row["fullname"];
						$status_verif            = $row["status_activity"];

						$data[] = array( 
									$Asset,
									$Inventory_Name,									
									$verif_due_date,
									$verif_date,
									$verifier, 
									$verif_int,
									$justification,
									$mmecertnum,
									$staff_name,			
									$status_verif);			
				}		
				echo $inventory = json_encode(array('data' => $data));
			}

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
}

?>