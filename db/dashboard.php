<?php


header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';


$conn = sqlsrv_connect( $serverName, $connectionInfo);
$today_date =  date('Y-m-d');
if( $conn ) {

			$action      = filter_var($_POST['action'], FILTER_SANITIZE_STRING); 

			if($action == "total_number_tg_status"){
				$sql = " SELECT status,	COUNT(*) AS 'total_num' FROM MME_ASSET  GROUP BY status";
			}
			if($action == "total_pending_health_check"){
				$sql = " SELECT COUNT(*) AS 'total_num' FROM MME_ASSET WHERE Health_Check_Date <= '$today_date' AND status = 'AVAILABLE' ";
			}			
			if($action == "total_tg"){
				$sql = "SELECT region,	COUNT(*) AS 'total_num'	FROM MME_ASSET	GROUP BY region";
			}
			if($action == "available_tg"){
				$sql = "SELECT region,	COUNT(*) AS 'total_num'	FROM MME_ASSET	WHERE status = 'AVAILABLE'	GROUP BY status,region	";
			}
			if($action == "unavailable_tg"){
				$sql = "SELECT	region,	COUNT(*) AS 'total_num'	FROM MME_ASSET	WHERE status = 'UNAVAILABLE' GROUP BY status,region	";
			}
			if($action == "faulty_tg"){
				$sql = "SELECT	region,	COUNT(*) AS 'total_num'	FROM MME_ASSET	WHERE status = 'FAULTY'	GROUP BY status,region";
			}
			if($action == "spare_tg"){
				$sql = "SELECT region, COUNT(*) AS 'total_num' FROM	MME_ASSET	WHERE status = 'SPARE' GROUP BY status,region";
			}
			if($action == "obsolete_tg"){
				$sql = "SELECT region, COUNT(*) AS 'total_num' FROM	MME_ASSET	WHERE status = 'OBSOLETE' GROUP BY status,region";
			}
			if($action == "ber_tg"){
				$sql = "SELECT region, COUNT(*) AS 'total_num' FROM	MME_ASSET	WHERE status = 'BER' GROUP BY status,region";
			}
			if($action == "hc_tg"){
				$sql = "SELECT region, COUNT(*) AS 'total_num' FROM	MME_ASSET	WHERE Health_Check_Date <= '$today_date' AND status = 'AVAILABLE' GROUP BY region";
			}
			if($action == "verif_tg"){
				$sql = "SELECT region, COUNT(*) AS 'total_num' FROM	MME_ASSET	WHERE Verif_Calib_Date <= '$today_date' GROUP BY region";
				$sql = "SELECT region, COUNT(*) AS 'total_num' FROM	MME_ASSET	WHERE status = 'PENDING VERIFICATION' GROUP BY region";
			}
			if($action == "inprog_verif_tg"){
				$sql = "SELECT region, COUNT(*) AS 'total_num' FROM	MME_ASSET	WHERE status = 'IN PROGRESS VERIFICATION' GROUP BY region";
			}
			if($action == "inprog_calib_tg"){
				$sql = "SELECT region, COUNT(*) AS 'total_num' FROM	MME_ASSET	WHERE status = 'IN PROGRESS CALIBRATION' GROUP BY region";
			}
			if($action == "confirmation_tg"){
				$sql = "SELECT region, COUNT(*) AS 'total_num' FROM	MME_ASSET	WHERE status = 'CONFIRMATION' GROUP BY region";
			}
			//MASTER TG
			if($action == "total_number_mtg_status"){
				$sql = " SELECT status,	COUNT(*) AS 'total_num' FROM MME_MASTER_ASSET2  GROUP BY status";
			}
			if($action == "total_pending_master_health_check"){
				$sql = " SELECT COUNT(*) AS 'total_num' FROM MME_MASTER_ASSET2 WHERE health_check_date <= '$today_date'  AND status = 'AVAILABLE' ";
			}
			if($action == "total_mtg"){
				$sql = "SELECT region,	COUNT(*) AS 'total_num'	FROM MME_MASTER_ASSET2	GROUP BY region";
			}
			if($action == "available_mtg"){
				$sql = "SELECT region,	COUNT(*) AS 'total_num'	FROM MME_MASTER_ASSET2	WHERE status = 'AVAILABLE'	GROUP BY status,region	";
			}
			if($action == "unavailable_mtg"){
				$sql = "SELECT	region,	COUNT(*) AS 'total_num'	FROM MME_MASTER_ASSET2	WHERE status = 'UNAVAILABLE' GROUP BY status,region	";
			}
			if($action == "faulty_mtg"){
				$sql = "SELECT region,	COUNT(*) AS 'total_num'	FROM MME_MASTER_ASSET2	WHERE status = 'FAULTY'	GROUP BY status,region	";
			}
			if($action == "spare_mtg"){
				$sql = "SELECT region,	COUNT(*) AS 'total_num'	FROM MME_MASTER_ASSET2	WHERE status = 'SPARE'	GROUP BY status,region	";
			}
			if($action == "obsolete_mtg"){
				$sql = "SELECT region,	COUNT(*) AS 'total_num'	FROM MME_MASTER_ASSET2	WHERE status = 'OBSOLETE'	GROUP BY status,region	";
			}
			if($action == "ber_mtg"){
				$sql = "SELECT region,	COUNT(*) AS 'total_num'	FROM MME_MASTER_ASSET2	WHERE status = 'BER' GROUP BY status,region	";
			}
			if($action == "hc_mtg"){
				$sql = "SELECT region, COUNT(*) AS 'total_num' FROM	MME_MASTER_ASSET2	WHERE health_check_date <= '$today_date' AND status = 'AVAILABLE' GROUP BY region";
			}
			if($action == "calib_mtg"){
				$sql = "SELECT region, COUNT(*) AS 'total_num' FROM	MME_MASTER_ASSET2	WHERE calibration_due_date <= '$today_date' GROUP BY region";
				$sql = "SELECT region, COUNT(*) AS 'total_num' FROM	MME_MASTER_ASSET2	WHERE status = 'PENDING CALIBRATION' GROUP BY region";
			}
			if($action == "inprog_calib_mtg"){
				$sql = "SELECT region, COUNT(*) AS 'total_num' FROM	MME_MASTER_ASSET2	WHERE status = 'IN PROGRESS CALIBRATION' GROUP BY region";
			}
			if($action == "confirmation_mtg"){
				$sql = "SELECT region, COUNT(*) AS 'total_num' FROM	MME_MASTER_ASSET2	WHERE status = 'CONFIRMATION' GROUP BY region";
			}
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false) {
				//die( print_r( sqlsrv_errors(), true) );	
				echo $inventory = json_encode(array('data' => 'FAILED'));
			}
			//die( print_r( sqlsrv_errors(), true) );
			if(sqlsrv_has_rows($stmt) != 1){ 
			   echo '{
					"sEcho": 1,
					"iTotalRecords": "0",
					"iTotalDisplayRecords": "0",
					"data": []
				}';
			}
			else {

				while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
					
					if($action == "total_number_tg_status" ||$action == "total_number_mtg_status" ){
						$status                   = $row["status"];
						$total_number             = $row["total_num"];

						$data[] = array($status, 
								$total_number);
						// $data[] = array(
						// 	'status' => $status,
						// 	'total' => $total_number);
					}
					else if($action == "total_pending_health_check" || $action == "total_pending_master_health_check"){
						$total_number             = $row["total_num"];
						if($total_number == 0){
							$total_number = '';
						}
						$data[] = array($total_number);
					}
					else {
						$region                   = $row["region"];
						$total_number             = $row["total_num"];

						$data[] = array(
									'name' => $region,
									'y' => $total_number);
					}

				}		
					
					echo $inventory = json_encode($data);
				// echo $data = json_encode(array(array("data" => $data)));
			}	

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
}

?>