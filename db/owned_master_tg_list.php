<?php



header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	

		$staff_id      = filter_var($_GET['staff_id'], FILTER_SANITIZE_STRING);

			
			$sql = "SELECT *
					FROM MME_MASTER_ASSET2
					WHERE MME_MASTER_ASSET2.staff_id LIKE '%".$staff_id."%'";

			
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false) {
				//die( print_r( sqlsrv_errors(), true) );		
				echo $inventory = json_encode(array('data' => 'FAILED'));
			}
			if(sqlsrv_has_rows($stmt) != 1){ 
		
				echo '{
					"sEcho": 1,
					"iTotalRecords": "0",
					"iTotalDisplayRecords": "0",
					"data": []
				}';                                 
			
			}
			else {
				$i = 1;
				while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {


					
						$asset_no                 = htmlspecialchars($row["master_mme_asset_id"]);
						$asset_inventory_number   = htmlspecialchars($row["master_mme_name"]);
						$asset_serialno           = htmlspecialchars($row["serial_no"]);
						$asset_health_check       = date_format($row["health_check_date"],'d-M-Y');
						if($asset_health_check == "01-Jan-1900"){
							$asset_health_check = "";
						}
						$asset_calib        = date_format($row["calibration_due_date"],'d-M-Y');
						if($asset_calib == "01-Jan-1900"){
							$asset_calib = "";
						}

						$asset_status             = htmlspecialchars($row["status"]);
						
						
					$data[] = array($asset_no,$asset_inventory_number,$asset_serialno,$asset_health_check,$asset_calib,$asset_status);
				}

				echo $userdetails = json_encode(array('data' => $data));
			}

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
}

?>