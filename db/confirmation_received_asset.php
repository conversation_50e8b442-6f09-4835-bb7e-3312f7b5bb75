<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header
include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	
			
			$returndate  = filter_var($_POST['returndate'], FILTER_SANITIZE_STRING);
			$mmeassetid  = filter_var($_POST['mmeassetid'], FILTER_SANITIZE_STRING);
			$remark      = filter_var($_POST['remark'], FILTER_SANITIZE_STRING);
			$type        = filter_var($_POST['type'], FILTER_SANITIZE_STRING);
			$borrow_date = filter_var($_POST['borrow_date'], FILTER_SANITIZE_STRING);
			
					
			$sql = "UPDATE [USERS_LOG] 
				SET Return_date='$returndate',Remark='$remark'
				WHERE LogID IN
				(SELECT TOP (1) LogID FROM [USERS_LOG]
				WHERE Asset_No='$mmeassetid' AND Return_date IS NULL AND Borrow_Date = '$borrow_date'
				ORDER BY LogID DESC)";
			

					
			
			$one_months_after = date('Y-m-d', strtotime($returndate. "1 Months"));
			
			if($type == "TG"){
				$sql .= "UPDATE [MME_ASSET] 
						SET status='AVAILABLE', Health_Check_Date='$one_months_after'
						WHERE Asset='$mmeassetid'";
			}
			else if($type == "MASTER_TG"){
				$sql .= "UPDATE [MME_MASTER_ASSET2] 
						SET status='AVAILABLE', health_check_date='$one_months_after'
						WHERE master_mme_asset_id='$mmeassetid'";
			}	
			
		
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				echo "FAILED";
			}else{
				echo "SUCCESS";
			}

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
}

?>