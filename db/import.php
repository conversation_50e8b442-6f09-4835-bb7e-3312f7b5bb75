<?php

//echo $_FILES['file']['name'];

include 'connection.php';
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	if(isset($_FILES['file']['name'])){

		$filename=$_FILES["file"]["tmp_name"];
		
		if($_FILES["file"]["size"] > 0){
		$file = fopen($filename, "r");
		$fp   = file($filename);
		
			for ($m=1; $m<count($fp); $m++ ) {
				fgetcsv($file);
				while (($Data = fgetcsv($file, ",")) !== FALSE){
					$a = isset($Data[0]) ? $Data[0] : null;
					$b = isset($Data[1]) ? $Data[1] : null;
					$c = isset($Data[2]) ? $Data[2] : null;
					$d = isset($Data[3]) ? $Data[3] : null;
					$e = isset($Data[4]) ? $Data[4] : null;
					$f = isset($Data[5]) ? $Data[5] : null;
					$g = isset($Data[6]) ? $Data[6] : null;
					$h = isset($Data[7]) ? $Data[7] : null;
					$i = isset($Data[8]) ? $Data[8] : null;
					$j = isset($Data[9]) ? $Data[9] : null;
					$k = isset($Data[10]) ? $Data[10] : null;
					$l = isset($Data[11]) ? $Data[11] : null;
					$m = isset($Data[12]) ? $Data[12] : null;
					$n = isset($Data[13]) ? $Data[13] : null;
					$o = isset($Data[14]) ? $Data[14] : null;
					//$o = isset(strtoupper($Data[14])) ? $Data[14] : null;
					$p = isset($Data[15]) ? $Data[15] : null;
					$q = isset($Data[16]) ? $Data[16] : null;
					$r = isset($Data[17]) ? $Data[17] : null;
					$s = isset($Data[18]) ? $Data[18] : null;
					$t = isset($Data[19]) ? $Data[19] : null;
					$u = isset($Data[20]) ? $Data[20] : null;
					$v = isset($Data[21]) ? $Data[21] : null;
					$w = isset($Data[22]) ? $Data[22] : null;
					
					$a = trim($a);
					$b = trim($b);
					$c = trim($c);
					$m = trim($m);
					$n = trim($n);
					$o = trim($o);
					
					$sql = "SELECT [MME_ASSET].Asset FROM [MME_ASSET] WHERE Asset ='$a'";
					$stmt = sqlsrv_query( $conn, $sql );
					if ($stmt) {					
					$rows = sqlsrv_has_rows( $stmt );
						if ($rows === true){
							$sql2 = "UPDATE [MME_ASSET]
									SET 
									Cost_Center='$b',
									Inventory_Number='$c',
									Asset_Description='$d',
									Asset_Description_editable='$e',
									Quantity='$f',
									address1='$g',
									address2='$h',
									address3='$i',
									address4='$j',
									First_Acquired_On='$k',
									Tagging_Date='$l',
									Serial_Number='$m',
									Asset_Verification_Status='$n',
									Verification_By='$o',
									Equipment_Model='$p',
									Useful_Life_month='$q',
									status='$r',
									unit='$s',
									territory='$t',								
									Health_Check_Date='$u',
									Verif_Calib_Date='$v',
									region='$w'								
									WHERE Asset='$a'";
						}
						else{
							$sql2 = "INSERT INTO [MME_ASSET] ( Asset,Cost_Center,Inventory_Number,Asset_Description,Asset_Description_editable,
														Quantity,address1,address2,address3,address4,
														First_Acquired_On,Tagging_Date,
														Serial_Number,
														Asset_Verification_Status,
														Verification_By,
														Equipment_Model,
														Useful_Life_month,
														status,
														unit,									
														territory,
														Health_Check_Date,
														Verif_Calib_Date,
														region
														) VALUES 
													  ('$a',
													  '$b',
													  '$c',
													  '$d',
													  '$e',
													  '$f',
													  '$g',
													  '$h',
													  '$i',
													  '$j',
													  '$k',
													  '$l',
													  '$m',
													  '$n',
													  '$o',
													  '$p',
													  '$q',
													  '$r',
													  '$s',
													  '$t',
													  '$u',
													  '$v',
													  '$w')";												  
							
						}
						
						$stmt2 = sqlsrv_query( $conn, $sql2 );
						if( $stmt2 === false ) {						
							die( print_r( sqlsrv_errors(), true));
						}else{
							$result = 'SUCCESS';	
						}
						
					}
					
				}
			}
			echo $result;
			fclose($file);
			//throws a message if data successfully imported to mysql database from excel file
			sqlsrv_free_stmt( $stmt);
			sqlsrv_free_stmt( $stmt2);
		
		}
		
	} else {
		echo "FAILED";
	}
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
	}


?>