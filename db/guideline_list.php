<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	
	// Retrieve email owner test gear
	// Main datatable
		/*$sql = "SELECT *					
				FROM MME_ASSET";*/
		
		$sql = "SELECT * FROM VERIFICATION_GUIDELINE";
				
					
		$stmt = sqlsrv_query( $conn, $sql );
			
		if( $stmt === false) {
			//die( print_r( sqlsrv_errors(), true) );		
		}
		
		if(sqlsrv_has_rows($stmt) != 1){ 
		  // echo "Invalid Username and Password.";
		  echo '{
					"sEcho": 1,
					"iTotalRecords": "0",
					"iTotalDisplayRecords": "0",
					"data": []
				}';    
		}
		else {
			$i = 1;
			while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
					$id                    = $i++;
					$gl_ref                = $row["gl_ref"];
					$test_equipment_type   = $row["test_equipment_type"];
					$guidelines_type       = $row["guidelines_type"];					
					$doc_owner             = $row["doc_owner"];
					//$doc_version           = $row["doc_version"];
					//$initial_date          = $row["initial_date"];
					$initial_date      = date_format($row["initial_date"],'d-M-Y');
					$revised_date      = date_format($row["revised_date"],'d-M-Y');
					//$file_size             = $row["file_size"];
					//$revised_date          = $row["revised_date"];
					$doc_link              = $row["doc_link"];
					
					$data[] = array($id, 
								$gl_ref,
								$test_equipment_type,
								$guidelines_type,
								$doc_owner, 
								//$doc_version,
								$initial_date,
								//$file_size,
								$revised_date,
								$doc_link);			
			}		
			echo $guideline = json_encode(array('data' => $data));
		}	

}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
	}

?>