<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

/*
$serverName = "172.20.49.37"; //serverName\instanceName
$connectionInfo = array( "Database"=>"COMME", "UID"=>"comme", "PWD"=>"%user456");*/
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	

	$staff_id            = filter_var($_POST['staff_id'], FILTER_SANITIZE_STRING); 	
	$training_name       = filter_var($_POST['training_name'], FILTER_SANITIZE_STRING); 	
	
	
		
	$sql = "DELETE FROM [MME_VERIFIER_lab]
			WHERE Staff_id='$staff_id' AND Training_Name='$training_name'
		";
	
	$stmt = sqlsrv_query( $conn, $sql );
	
	if( $stmt === false ) {
		//die( print_r( sqlsrv_errors(), true));
		echo $result = json_encode(array('data' => 'FAILED'));	
	} else{
		echo $result = json_encode(array('data' => 'SUCCESS'));	
	}
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
	}

?>