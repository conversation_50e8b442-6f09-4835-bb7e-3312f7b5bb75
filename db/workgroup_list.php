<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	

	$sql = "SELECT DISTINCT workgroup FROM [USERS]";
				
	$stmt = sqlsrv_query( $conn, $sql );
	
	
	while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
			//$id                   = $row["id"];
			$workgroup        = $row["workgroup"];
			//$vci         		  = $row["vci"];
			
			$data[] = array('data' => $workgroup);	
	
	}
    $alldata[] = array('data' => "ALL");	
    $newdata[] = array_merge($alldata,$data);	
	echo $inventory = json_encode($newdata);
}	

?>

