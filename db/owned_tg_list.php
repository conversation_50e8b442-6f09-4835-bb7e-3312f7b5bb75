<?php



header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	
		//$staff_id = $_GET["staff_id"];
		$staff_id      = filter_var($_GET['staff_id'], FILTER_SANITIZE_STRING);
		//sqlinjection
		//$staff_id   = $_GET['staff_id'];		
		
	
			/*$sql = "SELECT Test_Gear_Inventory.ID, Test_Gear_Inventory.Asset, Test_Gear_Inventory.Asset_Description, Test_Gear_Inventory.Asset_Description_editable,Test_Gear_Inventory.Serial_Number,Test_Gear_Inventory.status 
					FROM Test_Gear_Inventory
					WHERE Test_Gear_Inventory.Verification_By LIKE '%$staff_id%'";			*/
			
			$sql = "SELECT MME_ASSET.ID, MME_ASSET.Asset, MME_ASSET.Asset_Description, MME_ASSET.Asset_Description_editable,MME_ASSET.Serial_Number,MME_ASSET.status,MME_ASSET.Inventory_Number,MME_ASSET.Health_Check_Date,MME_ASSET.Verif_Calib_Date
					FROM MME_ASSET
					WHERE MME_ASSET.Verification_By LIKE '%".$staff_id."%'";
			
			//select * from log_provi where filename LIKE '%PS1026765474%';
			
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false) {
				//die( print_r( sqlsrv_errors(), true) );		
				echo $inventory = json_encode(array('data' => 'FAILED'));
			}
			if(sqlsrv_has_rows($stmt) != 1){ 
		
				echo '{
					"sEcho": 1,
					"iTotalRecords": "0",
					"iTotalDisplayRecords": "0",
					"data": []
				}';                                 
			
			}
			else {
				$i = 1;
				while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
					
					//	$mmeasset_id              = $row["MMEAssetID"];
						$id                       = $i++;
						$asset_no                 = htmlspecialchars($row["Asset"]);
						$asset_desc               = htmlspecialchars($row["Asset_Description"]);
						$asset_desc_editable      = htmlspecialchars($row["Asset_Description_editable"]);
						$asset_inventory_number   = htmlspecialchars($row["Inventory_Number"]);
						$asset_serialno           = htmlspecialchars($row["Serial_Number"]);
						//$asset_health_check       = $row["Health_Check_Date"];
						$asset_health_check       = date_format($row["Health_Check_Date"],'d-M-Y');
						if($asset_health_check == "01-Jan-1900"){
							$asset_health_check = "";
						}
						$asset_verif_calib        = date_format($row["Verif_Calib_Date"],'d-M-Y');
						if($asset_verif_calib == "01-Jan-1900"){
							$asset_verif_calib = "";
						}
						//$asset_verif_calib        = $row["Verif_Calib_Date"];
						$asset_status             = htmlspecialchars($row["status"]);
						
						//$asset_details = $row["Asset_Desc"] + " " + $row["Asset_Desc_editable"];
						
					$data[] = array($asset_no,$asset_inventory_number,$asset_desc."-".$asset_desc_editable,$asset_serialno,$asset_health_check,$asset_verif_calib,$asset_status);
				}
					//$data[] = array("1",$_POST["full_name"],"3","4");
				echo $userdetails = json_encode(array('data' => $data));
			}

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
}

?>