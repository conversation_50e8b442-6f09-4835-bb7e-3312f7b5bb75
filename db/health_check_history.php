<?php


header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';


$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	
			
			$mme_assetid      = filter_var($_GET['mme_assetid'], FILTER_SANITIZE_STRING); 
			
			$sql = "SELECT MME_HEALTH_CHECK.id, MME_HEALTH_CHECK.Asset, MME_HEALTH_CHECK.Inventory_Name, 
					MME_HEALTH_CHECK.health_check_date,MME_HEALTH_CHECK.status, MME_HEALTH_CHECK.condition,
					MME_HEALTH_CHECK.performed_by,MME_HEALTH_CHECK.remarks,MME_HEALTH_CHECK.performed_date,MME_HEALTH_CHECK.aros_id,
					[USERS].fullname,[USERS].email
					FROM MME_HEALTH_CHECK,[USERS]
					WHERE MME_HEALTH_CHECK.performed_by = [USERS].staff_id AND MME_HEALTH_CHECK.Asset = '".$mme_assetid."' ORDER BY id DESC";
			
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false) {
				//die( print_r( sqlsrv_errors(), true) );	
				echo $inventory = json_encode(array('data' => 'FAILED'));
			}
			
			if(sqlsrv_has_rows($stmt) != 1){ 
			   echo '{
					"sEcho": 1,
					"iTotalRecords": "0",
					"iTotalDisplayRecords": "0",
					"data": []
				}';
			}
			else {
				$id = 1;
				while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {						
						$log_id                  = $id++;
						$Asset                   = $row["Asset"];
						$Inventory_Name          = $row["Inventory_Name"];
						$health_check_date       = date_format($row["health_check_date"],'d-M-Y');
						$status                  = $row["status"];
						$condition               = $row["condition"];						
						$staff_id                = $row["performed_by"];
						$remarks                 = $row["remarks"];
						if($row["aros_id"] != ""){
							$remarks             = $row["remarks"]."AROS ID:".$row["aros_id"];
						}						
						$mmecertnum                = $row["mmecertnum"];
						$performed_date          = date_format($row["performed_date"],'d-M-Y');
						$staff_name              = $row["fullname"];
						$staff_email             = $row["email"];

						$data[] = array(
									$Asset,
									$Inventory_Name,
									$health_check_date, 
									$status,
									$condition,
									$staff_id,
									$remarks,
									$performed_date,
									$staff_name,
									$staff_email);			
				}		
				echo $inventory = json_encode(array('data' => $data));
			}	

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
}

?>