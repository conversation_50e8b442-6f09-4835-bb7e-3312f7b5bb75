<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header
include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	
		
		
			// $sql = "SELECT DISTINCT MME_ASSET.*, USERS.workgroup
			// 		FROM MME_ASSET
			// 		INNER JOIN USERS ON MME_ASSET.Verification_By=USERS.staff_id WHERE USERS.workgroup != '' AND MME_ASSET.status = 'SPARE'";
			
			
			// $sql = "SELECT DISTINCT MME_ASSET.*, USERS.workgroup
			// 		FROM MME_ASSET
			// 		INNER JOIN USERS ON MME_ASSET.Verification_By=USERS.staff_id WHERE MME_ASSET.status = 'SPARE'";

			$sql = "SELECT TB1.Cost_Center, TB1.region, TB1.Asset, TB1.Serial_Number, TB1.Asset_Description, TB1.Asset_Description_editable,TB1.Equipment_Model,TB1.Verification_By,TB1.status, TB2.remarks, TB2.id
					FROM MME_ASSET TB1 
					CROSS APPLY (
					SELECT DISTINCT TOP 1 TB2.* FROM MME_HEALTH_CHECK TB2 WHERE TB2.Asset = TB1.Asset AND TB1.status = 'SPARE'
					ORDER BY TB2.id DESC 
					) TB2";
								
		$stmt = sqlsrv_query( $conn, $sql );
			
		if( $stmt === false) {
			die( print_r( sqlsrv_errors(), true) );		
		}
		
		if(sqlsrv_has_rows($stmt) != 1){ 
			echo '{
				"sEcho": 1,
				"iTotalRecords": "0",
				"iTotalDisplayRecords": "0",
				"data": []
			}';   
		}
		else {
			$i = 1;
			while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
					$id                   = $i++;
					$cost_center          = $row["Cost_Center"];
					$region               = $row["region"];
					$asset                = $row["Asset"];
					$sn                   = $row["Serial_Number"];										
					$inventory_name       = $row["Asset_Description"].", ".$row["Asset_Description_editable"];
					$equipment_model      = $row["Equipment_Model"];
					$owner                = explode(" ", $row["Verification_By"]);
					if ($owner == null ) { $owner = "NULL"; } else { $owner = $row["Verification_By"]; } 
					$status               = $row["status"];
					$remark               = $row["remarks"];
					
					
					
										
					$data[] = array($id, 
								$cost_center,
								$region,
								$asset,
								$sn,
								$inventory_name, 
								$equipment_model,
								$owner,
								$status,			
								$remark);			
			}		
			echo $inventory = json_encode(array('data' => $data));
		}	

}
else{
		echo "Connection could not be established.<br />";
		// die( print_r( sqlsrv_errors(), true));
	}

?>