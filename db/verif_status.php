<?php



header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	
	//$assetno = $_POST["assetno"];
	$assetno = filter_var($_POST['assetno'], FILTER_SANITIZE_STRING);
	//$assetno     = $_POST['assetno'];

	$sql = "SELECT top 1 status_activity FROM [MME_VERIFICATION_CALIBRATION] WHERE Asset ='".$assetno."' ORDER BY id DESC";

	$stmt = sqlsrv_query( $conn, $sql );
	
	if( $stmt === false) {
		//die( print_r( sqlsrv_errors(), true) );
		echo $ownerdetail = json_encode(array('data' => 'FAILED'));		
	}
	else {
		while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
			
				$status_activity         = $row["status_activity"];
		
			$data[] = array(',',$status_activity,',');
		}		
		echo $ownerdetail = json_encode(array('data' => $data));
	}

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
}

?>