<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {

	$asset_id            = filter_var($_POST['asset_id'], FILTER_SANITIZE_STRING); 
	$performed_by        = filter_var($_POST['performed_by'], FILTER_SANITIZE_STRING);
	$inventory_name      = filter_var($_POST['inventory_name'], FILTER_SANITIZE_STRING); 
	$result              = filter_var($_POST['result'], FILTER_SANITIZE_STRING); 
	$activity            = filter_var($_POST['activity'], FILTER_SANITIZE_STRING);
	$tg_owner            = filter_var($_POST['tg_owner'], FILTER_SANITIZE_STRING);  
	$today_date          = filter_var($_POST['today_date'], FILTER_SANITIZE_STRING);  
	$remark              = filter_var($_POST['remark'], FILTER_SANITIZE_STRING);  
	
	if($result == "IN_PROGRESS_VERIFICATION"){
		$result2 = "IN PROGRESS VERIFICATION";
	}
	else if($result == "IN_PROGRESS_CALIBRATION"){
		$result2 = "IN PROGRESS CALIBRATION";
	}

			
	$sql = "INSERT INTO [MME_VERIFICATION_CALIBRATION] ( 
		Asset,
		Inventory_Name,
		status_activity,
		performed_by,
		[activity],
		Verification_date,
		status_tg,
		justification) 
		VALUES 
		('$asset_id',
		'$inventory_name',
		'$result2',
		'$performed_by',
		'$activity',
		'$today_date',
		'$result2',
		'$remark')";

		
	
	$sql .= "UPDATE [MME_ASSET]
			SET status = '$result2'
			WHERE Asset='$asset_id'";
			

			
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				die( print_r( sqlsrv_errors(), true));
			}
			else {
				$sql2 = "SELECT a.supervisor_email,a.fullname,a.supervisor_name,a.email,b.email AS email_1,b.fullname AS fullname_1
					FROM USERS a, USERS b
					WHERE a.staff_id='".$tg_owner."' AND b.staff_id='".$performed_by."'";
					
				$stmt = sqlsrv_query( $conn, $sql2 );
				
				if( $stmt === false) {
					echo '{
						"sEcho": 1,
						"iTotalRecords": "0",
						"iTotalDisplayRecords": "0",
						"data": []
					}';	
				} else {
					while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
	
							$supervisor_email      = $row["supervisor_email"];
							$owner_name            = $row["fullname"];
							$supervisor_name       = $row["supervisor_name"];
							$owner_email           = $row["email"];
							$staff_email           = $row["email_1"];
							$staff_name            = $row["fullname_1"];
	
	
						//$data[] = array("",$supervisor_email,$owner_name,$supervisor_name,$owner_email,$staff_email,$staff_name,"");
					}
					
					echo $result = json_encode(array(
						'data' => 'SUCCESS',
						'supervisor_name'=>$supervisor_name,
						'supervisor_email'=>$supervisor_email,
						'owner_name'=>$owner_name,
						'owner_email'=>$owner_email,
						'staff_email'=>$staff_email,
						'staff_name'=>$staff_name		
					));
				}
			}

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		// die( print_r( sqlsrv_errors(), true));
}

?>