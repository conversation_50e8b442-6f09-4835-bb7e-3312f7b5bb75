<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {

	$asset_id            = filter_var($_POST['asset_id'], FILTER_SANITIZE_STRING); 
	$performed_by        = filter_var($_POST['performed_by'], FILTER_SANITIZE_STRING); 
	$inventory_name      = filter_var($_POST['inventory_name'], FILTER_SANITIZE_STRING); 
	$verifdate           = filter_var($_POST['verifdate'], FILTER_SANITIZE_STRING); 
	$verifduedate        = filter_var($_POST['verifduedate'], FILTER_SANITIZE_STRING); 
	$verifinterval       = filter_var($_POST['verifinterval'], FILTER_SANITIZE_STRING); 
	$nextverifdate       = filter_var($_POST['nextverifdate'], FILTER_SANITIZE_STRING); 
	$justification       = filter_var($_POST['justification'], FILTER_SANITIZE_STRING); 
	$aros_id             = filter_var($_POST['aros_id'], FILTER_SANITIZE_STRING); 
	$status_verif        = filter_var($_POST['status_verif'], FILTER_SANITIZE_STRING); //FAULTY
	$attachment          = filter_var($_POST['attachment'], FILTER_SANITIZE_STRING); 
	$mmecertnum          = filter_var($_POST['mmecertnum'], FILTER_SANITIZE_STRING); 
	$result_verif        = filter_var($_POST['result_verif'], FILTER_SANITIZE_STRING);   //FAIL
	$activity            = filter_var($_POST['activity'], FILTER_SANITIZE_STRING); 
	$tg_owner            = filter_var($_POST['tg_owner'], FILTER_SANITIZE_STRING); 
	$verifier            = filter_var($_POST['verifier'], FILTER_SANITIZE_STRING); 
	
	

			
	$sql = "INSERT INTO [MME_VERIFICATION_CALIBRATION] ( 
		Asset,Inventory_Name,Verification_date,
		Verification_duedate,Verification_interval,
		justification,mmecertnum,status_activity,
		performed_by,attachment,[activity],
		aros_id,status_tg,calibrator) 
		VALUES 
		('$asset_id',
		'$inventory_name',
		'$verifdate',
		'$verifduedate',
		'$verifinterval',
		'$justification',
		'$mmecertnum',
		'$result_verif',
		'$performed_by',
		'$attachment',
		'$activity',
		'$aros_id',
		'$status_verif',
		'$verifier')";

		
	
	$sql .= "UPDATE [MME_ASSET]
			SET Verif_Calib_Date='$nextverifdate',
			status = '$status_verif'
			WHERE Asset='$asset_id'";
			

			
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				//die( print_r( sqlsrv_errors(), true));
			}
			else {
				$sql2 = "SELECT a.supervisor_email,a.fullname,a.supervisor_name,a.email,b.email AS email_1,b.fullname AS fullname_1
					FROM USERS a, USERS b
					WHERE a.staff_id='".$tg_owner."' AND b.staff_id='".$performed_by."'";
					
				$stmt = sqlsrv_query( $conn, $sql2 );
				
				if( $stmt === false) {
					echo '{
						"sEcho": 1,
						"iTotalRecords": "0",
						"iTotalDisplayRecords": "0",
						"data": []
					}';	
				} else {
					while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
	
							$supervisor_email      = htmlspecialchars($row["supervisor_email"]);
							$owner_name            = htmlspecialchars($row["fullname"]);
							$supervisor_name       = htmlspecialchars($row["supervisor_name"]);
							$owner_email           = htmlspecialchars($row["email"]);
							$staff_email           = htmlspecialchars($row["email_1"]);
							$staff_name            = htmlspecialchars($row["fullname_1"]);
	
	
						//$data[] = array("",$supervisor_email,$owner_name,$supervisor_name,$owner_email,$staff_email,$staff_name,"");
					}
					
					echo $result = json_encode(array(
						'data' => 'SUCCESS',
						'supervisor_name'=>$supervisor_name,
						'supervisor_email'=>$supervisor_email,
						'owner_name'=>$owner_name,
						'owner_email'=>$owner_email,
						'staff_email'=>$staff_email,
						'staff_name'=>$staff_name		
					));
				}
			}

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		// die( print_r( sqlsrv_errors(), true));
}

?>