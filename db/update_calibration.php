<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {

	$asset_id            = filter_var($_POST['asset_id'], FILTER_SANITIZE_STRING); 
	$performed_by        = filter_var($_POST['performed_by'], FILTER_SANITIZE_STRING); 
	$inventory_name      = filter_var($_POST['inventory_name'], FILTER_SANITIZE_STRING); 
	$calibdate           = filter_var($_POST['calibdate'], FILTER_SANITIZE_STRING); 
	$calibduedate        = filter_var($_POST['calibduedate'], FILTER_SANITIZE_STRING); 
	$calibinterval       = filter_var($_POST['calibinterval'], FILTER_SANITIZE_STRING); 
	$nextcalibdate       = filter_var($_POST['nextcalibdate'], FILTER_SANITIZE_STRING); 
	$justification       = filter_var($_POST['justification'], FILTER_SANITIZE_STRING); 
	$aros_id             = filter_var($_POST['aros_id'], FILTER_SANITIZE_STRING); 
	$status_calib        = filter_var($_POST['status_calib'], FILTER_SANITIZE_STRING); 
	$attachment          = filter_var($_POST['attachment'], FILTER_SANITIZE_STRING); 
	$mmecertnum          = filter_var($_POST['mmecertnum'], FILTER_SANITIZE_STRING); 
	$result_calib        = filter_var($_POST['result_calib'], FILTER_SANITIZE_STRING); 
	$activity            = filter_var($_POST['activity'], FILTER_SANITIZE_STRING); 
	$tg_owner            = filter_var($_POST['tg_owner'], FILTER_SANITIZE_STRING); 
	$calibrator          = filter_var($_POST['calibrator'], FILTER_SANITIZE_STRING); 
	
	

			
	$sql = "INSERT INTO [MME_VERIFICATION_CALIBRATION] ( 
		Asset,Inventory_Name,Verification_date,
		Verification_duedate,Verification_interval,
		justification,mmecertnum,status_activity,
		performed_by,attachment,[activity],
		aros_id,status_tg,calibrator) 
		VALUES 
		('$asset_id',
		'$inventory_name',
		'$calibdate',
		'$calibduedate',
		'$calibinterval',
		'$justification',
		'$mmecertnum',
		'$result_calib',
		'$performed_by',
		'$attachment',
		'$activity',
		'$aros_id',
		'$status_calib',
		'$calibrator')";

		
	
	$sql .= "UPDATE [MME_ASSET]
			SET Verif_Calib_Date='$nextcalibdate',
			status = '$status_calib'
			WHERE Asset='$asset_id'";
			

			
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				die( print_r( sqlsrv_errors(), true));
			}
			else {
				$sql2 = "SELECT a.supervisor_email,a.fullname,a.supervisor_name,a.email,b.email AS email_1,b.fullname AS fullname_1
					FROM USERS a, USERS b
					WHERE a.staff_id='".$tg_owner."' AND b.staff_id='".$performed_by."'";
					
				$stmt = sqlsrv_query( $conn, $sql2 );
				
				if( $stmt === false) {
					echo '{
						"sEcho": 1,
						"iTotalRecords": "0",
						"iTotalDisplayRecords": "0",
						"data": []
					}';	
				} else {
					while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
	
							$supervisor_email      = $row["supervisor_email"];
							$owner_name            = $row["fullname"];
							$supervisor_name       = $row["supervisor_name"];
							$owner_email           = $row["email"];
							$staff_email           = $row["email_1"];
							$staff_name            = $row["fullname_1"];
	
	
						//$data[] = array("",$supervisor_email,$owner_name,$supervisor_name,$owner_email,$staff_email,$staff_name,"");
					}
					
					echo $result = json_encode(array(
						'data' => 'SUCCESS',
						'supervisor_name'=>$supervisor_name,
						'supervisor_email'=>$supervisor_email,
						'owner_name'=>$owner_name,
						'owner_email'=>$owner_email,
						'staff_email'=>$staff_email,
						'staff_name'=>$staff_name
					));
				}
			}

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		// die( print_r( sqlsrv_errors(), true));
}

?>