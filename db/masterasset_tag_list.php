<?php



header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);
$q      = filter_var($_GET['q'], FILTER_SANITIZE_STRING);	

if( $conn ) {	

	$sql = "SELECT DISTINCT master_mme_name FROM [MME_MASTER_ASSET2] WHERE MME_MASTER_ASSET2.master_mme_name LIKE '%".$q."%'";
				
	$stmt = sqlsrv_query( $conn, $sql );
		
	while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
			//$id                   = $row["id"];
			$master_mme_name      = $row["master_mme_name"];
			//$vci         		  = $row["vci"];
			
			$data[] = array('data' => $master_mme_name);			
	}		
	$alldata[] = array('data' => "ALL");	
    $newdata[] = array_merge($alldata,$data);	
	echo $inventory = json_encode($newdata);
}	

?>

