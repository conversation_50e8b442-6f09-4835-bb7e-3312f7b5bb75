<?php



header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	
	
	$master_mme_asset_no      = filter_var($_POST['master_mme_asset_no'], FILTER_SANITIZE_STRING); 

		
	$sql = "DELETE FROM [MME_MASTER_ASSET2]
			WHERE master_mme_asset_id='".$master_mme_asset_no."'";
	
	$stmt = sqlsrv_query( $conn, $sql );
	
	if( $stmt === false ) {
		//die( print_r( sqlsrv_errors(), true));
		echo $result = json_encode(array('data' => 'FAILED'));	
	}else {
		echo $result = json_encode(array('data' => 'SUCCESS'));	
	}

}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
	}

?>