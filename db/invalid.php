<?php



header('Content-Type: application/json;charset=utf-8'); //json header
session_start();

include 'connection.php';
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {

    date_default_timezone_set("Asia/Kuala_Lumpur");
    $staff_id     = filter_var($_POST['staff_id'], FILTER_SANITIZE_STRING);
    $date         = date('Y-m-d H:i');
			
    
    $sql = "SELECT [USERS].counter, [USERS].last_logon FROM [USERS] WHERE staff_id = '$staff_id' ";
    
    $stmt = sqlsrv_query( $conn, $sql );
        
    if( $stmt === false) {
        die( print_r( sqlsrv_errors(), true) );		
    }
    else {
        while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {

            $counter        = $row["counter"];
            $time_stamp     = date_format($row["last_logon"],'Y-m-d H:i');
            $new_counter = $counter + 1;
            
            if($counter<3){
                $sql2 = "UPDATE [USERS]
                        SET 
                        last_logon ='$date',
                        counter = '$new_counter'
                        WHERE staff_id='$staff_id'";

                $stmt2 = sqlsrv_query( $conn, $sql2 );
                            
                if( $stmt2 === false ) {
                    die( print_r( sqlsrv_errors(), true));
                }

                $result = json_encode(array('data' => 'SUCCESS' ));

            }					
            else{
                $sql2 = "UPDATE [USERS]
                        SET 
                        counter = '$new_counter',
                        status = 'LOCKED'
                        WHERE staff_id='$staff_id'";

                $stmt2 = sqlsrv_query( $conn, $sql2 );
                            
                if( $stmt2 === false ) {
                    die( print_r( sqlsrv_errors(), true));
                }

                $result = json_encode(array('data' => 'LOCKED' ));
                
            }        
				
        }
        echo $result;
        //echo $result = json_encode(array('data' => 'SUCCESS' ));	
    }		
}
else{
    echo "Connection could not be established.<br />";
    die( print_r( sqlsrv_errors(), true));
}
?>


