<?php


if(isset($_FILES["file"]["name"])){
	$image = $_FILES["file"][ "name" ];
	$a = explode(".",$image);
	$upload_image = $a[0];

	$folder = "../attachment/images/";
	move_uploaded_file($_FILES["file"]["tmp_name"], "$folder".$_FILES["file"]["name"]);
	$uploadimage = $folder.$_FILES["file"]["name"];
	$newname = $_FILES["file"]["name"];

	if ($image == ""){
		$resize_image = "";
	}
	else{
		$resize_image = $folder.$newname.""; 
	}
	$actual_image = $folder.$newname;

	list( $width,$height ) = getimagesize( $uploadimage );
	
	$newwidth = 200;
	$newheight = 200;
	$thumb = imagecreatetruecolor( $newwidth, $newheight );
	$source = imagecreatefromjpeg($actual_image);
	
	imagecopyresized($thumb, $source, 0, 0, 0, 0, $newwidth, $newheight, $width, $height);
	imagejpeg( $thumb, $resize_image, 100 ); 
	//echo $out_image=addslashes(file_get_contents($resize_image));
	echo "SUCCESS";

}



?>