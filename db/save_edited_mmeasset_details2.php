<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

/*
$serverName = "172.20.49.37"; //serverName\instanceName
$connectionInfo = array( "Database"=>"COMME", "UID"=>"comme", "PWD"=>"%user456");*/
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	
			/*$asset_no                 = $_POST['mme_asset_no']; 						
			$cost_center              = $_POST['cost_center']; 						
			$inventory_name           = $_POST['inventory_name']; 						
			$asset_desc               = $_POST['asset_desc']; 						
			$asset_desc_editable      = $_POST['asset_desc_editable']; 						
			$quantity                 = $_POST['quantity']; 						
			$address1                 = $_POST['address1']; 						
			$address2                 = $_POST['address2']; 						
			$address3                 = $_POST['address3']; 						
			$address4                 = $_POST['address4']; 						
			$first_acq_date           = $_POST['first_acq_date']; 						
			$tagg_date                = $_POST['tagg_date']; 						
			$serial_no                = $_POST['serial_no']; 						
			$asset_verif_stat         = $_POST['asset_verif_stat']; 						
			$verif_by                 = $_POST['verif_by']; 						
			$equipment_model          = $_POST['equipment_model']; 						
			$useful_life_month        = $_POST['useful_life_month']; 						
			$status                   = $_POST['status']; 						
			$unit                     = $_POST['unit']; 						
			$territory                = $_POST['territory']; 						
			$region                   = $_POST['region']; 						
			$next_health_check_date   = $_POST['next_health_check_date']; 						
			$next_verif_date          = $_POST['next_verif_date']; 	*/

			$asset_no              = filter_var($_POST['mme_asset_no'], FILTER_SANITIZE_STRING);
			$cost_center           = filter_var($_POST['cost_center'], FILTER_SANITIZE_STRING);
			$inventory_name        = filter_var($_POST['inventory_name'], FILTER_SANITIZE_STRING);
			$asset_desc            = filter_var($_POST['asset_desc'], FILTER_SANITIZE_STRING);
			$asset_desc_editable   = filter_var($_POST['asset_desc_editable'], FILTER_SANITIZE_STRING);
			$quantity              = filter_var($_POST['quantity'], FILTER_SANITIZE_STRING);
			$address1              = filter_var($_POST['address1'], FILTER_SANITIZE_STRING);
			$address2              = filter_var($_POST['address2'], FILTER_SANITIZE_STRING);
			$address3              = filter_var($_POST['address3'], FILTER_SANITIZE_STRING);
			$address4              = filter_var($_POST['address4'], FILTER_SANITIZE_STRING);
			$first_acq_date        = filter_var($_POST['first_acq_date'], FILTER_SANITIZE_STRING);
			$tagg_date             = filter_var($_POST['tagg_date'], FILTER_SANITIZE_STRING);
			$serial_no             = filter_var($_POST['serial_no'], FILTER_SANITIZE_STRING);
			$asset_verif_stat      = filter_var($_POST['asset_verif_stat'], FILTER_SANITIZE_STRING);
			$verif_by              = filter_var($_POST['verif_by'], FILTER_SANITIZE_STRING);
			$equipment_model       = filter_var($_POST['equipment_model'], FILTER_SANITIZE_STRING);
			$useful_life_month     = filter_var($_POST['useful_life_month'], FILTER_SANITIZE_STRING);
			$status                = filter_var($_POST['status'], FILTER_SANITIZE_STRING);
			$unit                  = filter_var($_POST['unit'], FILTER_SANITIZE_STRING);
			$territory             = filter_var($_POST['territory'], FILTER_SANITIZE_STRING);
			$region                = filter_var($_POST['region'], FILTER_SANITIZE_STRING);
			$next_health_check_date  = filter_var($_POST['next_health_check_date'], FILTER_SANITIZE_STRING);
			$next_verif_date         = filter_var($_POST['next_verif_date'], FILTER_SANITIZE_STRING);
			//$status                  = filter_var($_POST['status'], FILTER_SANITIZE_STRING);

			if($region == "SFC "){
				$region = "SFC & BPQM";
			}
		
			$sql = "UPDATE [MME_ASSET]
					SET 
					Cost_Center='$cost_center',
					Inventory_Number='$inventory_name',
					Asset_Description='$asset_desc',
					Asset_Description_editable='$asset_desc_editable',
					Quantity='$quantity',
					address1='$address1',
					address2='$address2',
					address3='$address3',
					address4='$address4',
					First_Acquired_On='$first_acq_date',
					Tagging_Date='$tagg_date',
					Serial_Number='$serial_no',
					Asset_Verification_Status='$asset_verif_stat',
					Verification_By='$verif_by',
					Equipment_Model='$equipment_model',
					Useful_Life_month='$useful_life_month',
					status='$status',
					unit='$unit',
					territory='$territory',
					region='$region',
					Health_Check_Date='$next_health_check_date',
					Verif_Calib_Date='$next_verif_date'				
					WHERE Asset='$asset_no'";
			
		
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				//die( print_r( sqlsrv_errors(), true));
				echo $result = json_encode(array('data' => 'FAILED'));	
			}
			else {
				echo $result = json_encode(array('data' => 'SUCCESS'));		
			}

}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
	}

?>