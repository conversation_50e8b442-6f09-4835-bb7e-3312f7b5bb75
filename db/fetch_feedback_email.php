<?php



header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';


/*
$serverName = "172.20.49.37"; //serverName\instanceName
$connectionInfo = array( "Database"=>"COMME", "UID"=>"comme", "PWD"=>"%user456");*/
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	
		
			$feedback_id      = filter_var($_POST['feedback_id'], FILTER_SANITIZE_STRING); 
			$login_id         = filter_var($_POST['login_id'], FILTER_SANITIZE_STRING); 
			
		
			//$sql = "SELECT email FROM [USERS] WHERE staff_id ='$staff_id' ";
			//$sql .= "SELECT contact_number FROM [USERS] WHERE staff_id ='$owner_id'";
			
			$sql = "SELECT  (
						SELECT email FROM [USERS] WHERE staff_id = '".$login_id."' 
							) AS admin,
							(
						SELECT email FROM [USERS] WHERE staff_id ='".$feedback_id."'
							) AS respondent_email,
								(
						SELECT fullname FROM [USERS] WHERE staff_id ='".$feedback_id."'
							) AS respondent_name";
				
				$stmt = sqlsrv_query( $conn, $sql );
				
				if( $stmt === false) {
					//die( print_r( sqlsrv_errors(), true) );	
					echo '{
						"sEcho": 1,
						"iTotalRecords": "0",
						"iTotalDisplayRecords": "0",
						"data": []
					}';						
				}
				else {
					while( $row = sqlsrv_fetch_array( $stmt, SQLSRV_FETCH_ASSOC) ) {
	
							$email_admin         = $row["admin"];
							$email_respondent    = $row["respondent_email"];
							$email_name          = $row["respondent_name"];
							
						$data[] = array("",$email_admin,$email_respondent,$email_name,"");
					}
						//$data[] = array("1",$_POST["full_name"],"3","4");
					echo $respondentdetails = json_encode(array('data' => $data));
				}
		
			

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		die( print_r( sqlsrv_errors(), true));
}

?>