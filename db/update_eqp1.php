<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header

include 'connection.php';

/*
$serverName = "************"; //serverName\instanceName
$connectionInfo = array( "Database"=>"COMME", "UID"=>"comme", "PWD"=>"%user456");*/
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {
	
			/*$mme_equipment   = $_POST['mme_equipment'];    //name required for item not in the db  
			$vci             = $_POST['vci'];*/
			
			$mme_equipment   = filter_var($_POST['mme_equipment'], FILTER_SANITIZE_STRING);
			$vci             = filter_var($_POST['vci'], FILTER_SANITIZE_STRING);
			
			$mme_equipment   = utf8_decode($mme_equipment);
			$vci             = utf8_decode($vci);

			
			$sql = "INSERT INTO [MME_EQP1] ( mme_equipment,vci) VALUES 
					('$mme_equipment','$vci')";
			
		
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				die( print_r( sqlsrv_errors(), true));
			}
			echo $result = json_encode(array('data' => 'SUCCESS'));
		
			

		sqlsrv_free_stmt( $stmt);
}
else{
		echo "Connection could not be established.<br />";
		die( print_r( sqlsrv_errors(), true));
}

?>