<?php

/*
 * This file is part of SwiftMailer.
 * (c) 2004-2009 <PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/**
 * Signed Message, message that can be signed using a signer.
 * 
 * This class is only kept for compatibility
 *
 * @package    Swift
 * @subpackage Signed
 *
 * <AUTHOR> <<EMAIL>>
 * @deprecated
 */
class Swift_SignedMessage extends Swift_Message
{

}
