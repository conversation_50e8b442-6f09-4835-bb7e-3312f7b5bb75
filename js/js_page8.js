

function checkEmail(str)
{
    var re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if(!re.test(str)){
		var error = "INVALID";
		$('#staff_email').hover(function(){
				$('#staff_email').tooltip('hide');
		});
										
		if (error != ""){
			$("#staff_email").tooltip({'trigger':'manual','title':'Please Insert Valid Staff Email','placement':'top'})
					 .tooltip('show');
			//$("#submit_profile").hide();
		}
		
	}
		
	
    //alert("Please enter a valid email address");
}

function checkEmail_boss(str)
{
    var re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if(!re.test(str)){
		var error = "INVALID";
		$('#supervisor_em').hover(function(){
				$('#supervisor_em').tooltip('hide');
		});
										
		if (error != ""){
			$("#supervisor_em").tooltip({'trigger':'manual','title':'Please Insert Valid Supervisor Email','placement':'top'})
					 .tooltip('show');
			//$("#submit_profile").hide();
		}
		
	}
		
	
    //alert("Please enter a valid email address");
}

//date
function reformatDateString(ds) {
	var months = {'01':'Jan','02':'Feb','03':'Mar','04':'Apr','05':'May','06':'Jun',
					'07':'Jul','08':'Aug','09':'Sep','10':'Oct','11':'Nov','12':'Dec'};
	var b = ds.split('-');
	return b[2] + '-' + months[b[1].toLowerCase()] + '-' + b[0] ;
}

//send email
function send_email(a,b,c,d,e,f,g,h,i,j,k){
	var supervisor_name = a;
	var supervisor_email = b;
	var owner_name = c;
	var owner_email = d;
	var staff_email = e;
	var staff_name = f;
	var email_task = g;
	var asset_id = h;
	var inventory_name = i;
	var staff_id = j;
	var new_stat = k;

	if (email_task == "MONTHLY HEALTH CHECK"){
		var modal_task = "health_check";
	}
	else if (email_task == "MASTER MONTHLY HEALTH CHECK"){
		var modal_task = "master_mme_health_check";
	}
	else if (email_task == "VERIFICATION" || email_task == "IN PROGRESS VERIFICATION" ||email_task == "CALIBRATION" || email_task == "IN PROGRESS CALIBRATION" ){
		var modal_task = "verif_calib";
	}
	else if (email_task == "IN PROGRESS MASTER CALIBRATION" || email_task == "MASTER CALIBRATION"){
		var modal_task = "master_mme_calib";
	}
	$('#success_modal').modal("show");
	$('#success_modal').on('hidden.bs.modal', function () {
		$('#'+modal_task+'').modal("hide");
		if (modal_task == "health_check" || modal_task == "verif_calib"){
			$('#own_asset_mme_list').DataTable().ajax.reload();
		}
		else if(modal_task == "master_mme_health_check" || modal_task == "master_mme_calib"){
			$('#own_master_asset_mme_list').DataTable().ajax.reload();	
		}				
		
	})

	$.ajax({
		url: "email_notification/activity_email.php",
		type: "GET",
		data:"asset_id="+asset_id+"&inventory_name="+inventory_name+"&performed_by="+staff_id+"&supervisor_email="+
			supervisor_email+"&performed_name="+staff_name+"&supervisor_name="+supervisor_name+"&staff_email="+
			staff_email+"&owner_email="+owner_email+"&activity="+email_task+"&status="+new_stat,
		success:function(data_response){
			console.log(data_response);
			/* $('#success_modal').modal("show");
			$('#success_modal').on('hidden.bs.modal', function () {
				$('#'+modal_task+'').modal("hide");
				if (modal_task == "health_check" || modal_task == "verif_calib"){
					$('#own_asset_mme_list').DataTable().ajax.reload();
				}
				else if(modal_task == "master_mme_health_check" || modal_task == "master_mme_calib"){
					$('#own_master_asset_mme_list').DataTable().ajax.reload();	
				}				
				
			}) */
			
		}
		/*,
		error: function (request, status, error) {
			$('#error_modal').modal("show");
		}*/
	});	

}

//Health Check
function health_check_action(cost_center,asset_id,serial_no,inventory_name,health_check,verif_calib,status,staff_id,today){

	$('#health_check').modal("show");
	$("#loading_hc_icon").hide();
	
	$("#mmeasset_modal").val(asset_id);
	$("#inventoryname_modal").val(inventory_name);
	$("#healthcheckdate_modal").val(health_check);
	$("#status_modal_hc").val(status);
	$("#performedby_modal").val(staff_id);

	$("#new_stat_modal").change(function() {					
		if ($("#new_stat_modal").val() == "BER" || $("#new_stat_modal").val() == "LOST" || $("#new_stat_modal").val() == "OBSOLETE"){
			$("#aros_det").show();
		}else {
			$("#aros_det").hide();
		}
	});
	


	$('#error_modal').on('hidden.bs.modal', function () {
		$("#save_health_check").show()
		$("#close_health_check").show()
	})

}

	//verification_calib_action(cost_center,asset_id,serial_no,inventory_name,health_check,verif_calib,status,staff_id,today);
			
function verification_calib_action(cost_center,asset_id,serial_no,inventory_name,health_check,verif_calib,status,staff_id,today){
	var new_today_date = today.toISOString().slice(0,10);

	$('#nextverifdate').datepicker();
	$('#nextcalibdate').datepicker();

	$('#verif_calib').modal("show");
	$('#loading_verif_icon').hide();
	$('#loading_calib_icon').hide();
	$("#verification").hide();
	$("#calibration").hide();
	
	$('#verif_button').on("click",function(){
		$("#verification").show();
		$("#calibration").hide();

		$("#mmeassetverif").val(asset_id);
		$("#inventorynameverif").val(inventory_name);
		$("#verifdate").val(new_today_date);
		$("#verifduedate").val(verif_calib);


		var update_in_progress = '0';

		$("#verifinterval").keyup(function () {
			var year = $("#verifinterval").val();
			var new_date_verif = new Date(new Date().setFullYear(+new Date().getFullYear() + +year)).toISOString().slice(0,10);
			$("#nextverifdate").val(reformatDateString(new_date_verif));							
		});


		$("#result_verif").change(function() {
			if( $("#result_verif").val() == 'IN_PROGRESS_VERIFICATION'){
				$("#verif_result_input_filled").hide();
				$("#remarks_verif").show();
				update_in_progress = '1';
				$('#update_in_progress').val(update_in_progress);
			} 
			else {
				$("#verif_result_input_filled").show();
			}
		});
		
		$("#verif_stat_modal").change(function() {
			if ($("#verif_stat_modal").val() == "BER" || $("#verif_stat_modal").val() == "LOST" || $("#verif_stat_modal").val() == "OBSOLETE"){
				$("#aros_verif_det").show();
			}else {
				$("#aros_verif_det").hide();
			}
		});

		
		$('#error_modal').on('hidden.bs.modal', function () {
			$("#save_verif").show()
			$("#close_verif").show()
		})

	});

	$('#calib_button').click(function() {
		$("#calibration").show();
		$("#verification").hide();

		$("#mmeassetcalib").val(asset_id);
		$("#inventorynamecalib").val(inventory_name);
		$("#calibdate").val(new_today_date);
		$("#calibduedate").val(verif_calib);

		var update_in_progress = '0';

		$("#calibinterval").keyup(function () {
			var year = $("#calibinterval").val();
			var new_date_calib = new Date(new Date().setFullYear(+new Date().getFullYear() + +year)).toISOString().slice(0,10);
			$("#nextcalibdate").val(reformatDateString(new_date_calib));
		});

		$("#result_calib").change(function() {
			if( $("#result_calib").val() == 'IN_PROGRESS_CALIBRATION'){
				$("#calib_result_input_filled").hide();
				$("#remarks_calib").show();
				update_in_progress = '1';
				$('#update_in_progress_calib').val(update_in_progress);
			}
			else {
				$("#calib_result_input_filled").show();
			}
		});

		$("#calib_stat_modal").change(function() {
			if ($("#calib_stat_modal").val() == "BER" || $("#calib_stat_modal").val() == "LOST" || $("#calib_stat_modal").val() == "OBSOLETE"){
				$("#aros_calib_det").show();
			}else {
				$("#aros_calib_det").hide();
			}
		});

		
		$('#error_modal').on('hidden.bs.modal', function () {
			$("#save_calib").show()
			$("#close_calib").show()
		})

		
	});





}


//Master Health Check
function master_health_check_action(vc,cost_center,master_mme_asset_id,master_mme_name,serial_number,health_check,staff_id,vc_location,status,today){
	$('#master_mme_health_check').modal("show");
	$("#loading_icon").hide();

	$("#master_mmeasset_modal").val(master_mme_asset_id);
	$("#master_mme_inventoryname_modal").val(master_mme_name);
	$("#master_mme_healthcheckdate_modal").val(health_check);
	$("#master_mme_status_modal").val(status);
	$("#master_mme_performedby_modal").val(staff_id);

	$("#master_mme_new_stat_modal").change(function() {					
		if ($("#master_mme_new_stat_modal").val() == "BER" || $("#master_mme_new_stat_modal").val() == "LOST" || $("#master_mme_new_stat_modal").val() == "OBSOLETE"){
			$("#master_aros_det").show();
		}else {
			$("#master_aros_det").hide();
		}
	});

	$('#error_modal').on('hidden.bs.modal', function () {
		$("#master_mme_save_health_check").show()
		$("#master_mme_close_health_check").show()
	})

}

function calibration_action(vc,cost_center,master_mme_asset_id,master_mme_name,serial_number,calib,staff_id,vc_location,today){
	$('#master_mme_calib').modal("show");
	$("#calib_loading_icon").hide();
	var new_today_date = today.toISOString().slice(0,10);

	$("#master_mme_assetcalib").val(master_mme_asset_id);
	$("#master_mme_inventorynamecalib").val(master_mme_name);
	$("#master_mme_calibdate").val(new_today_date);
	$("#master_mme_calibduedate").val(calib);

	var update_in_progress = '0';

	$("#master_mme_calibinterval").on("keyup",function(){
		var year = $("#master_mme_calibinterval").val();
		var new_date_calib = new Date(new Date().setFullYear(+new Date().getFullYear() + +year)).toISOString().slice(0,10);
		$("#master_mme_nextcalibdate").val(reformatDateString(new_date_calib));
	});

	$("#master_mme_result_calib").on("change",function(){
		if( $("#master_mme_result_calib").val() == 'IN_PROGRESS_CALIBRATION'){
			$("#master_mme_calib_result_input_filled").hide();
			$("#master_mme_remarks_calib").show();
			update_in_progress = '1';
			$('#update_in_progress_master_calib').val(update_in_progress);
		}
		else {
			$("#master_mme_calib_result_input_filled").show();
			$("#master_mme_remarks_calib").hide();
		}
	});

	$("#master_mme_calib_stat_modal").on("change",function(){
		if ($("#master_mme_calib_stat_modal").val() == "BER" || $("#master_mme_calib_stat_modal").val() == "LOST" || $("#master_mme_calib_stat_modal").val() == "OBSOLETE"){
			$("#master_mme_aros_calib_det").show();
		}else {
			$("#master_mme_aros_calib_det").hide();
		}
	});

	$('#error_modal').on('hidden.bs.modal', function () {
		$("#master_mme_save_calib").show()
		$("#master_mme_close_calib").show()
	})



}

$(document).ready(function(){
	//$("#asset_mme_list").DataTable();	
	var today   = new Date() 
	
	var parts = window.location.href.split( '=' );
	var staff_id = parts[1];
	
	if ($("#staff_id_session").text() == staff_id ){
		$("#edit_button").show();	
	}
	
	var c_id = $("#staff_id_session").text();
	var c_work = $("#workgroup_session").text();

	
	$("#staffid").val(staff_id);
	
	//user details
	$.ajax({
		url: "db/user_details.php",
		data: 'staff_id='+staff_id,
		type: "POST",
		datatype:"JSON",
		success:function(data_response){
			//console.log(data_response)
			//console.log(data_response.fullname)
			$("#staff_name").text(data_response.fullname);
			$("#staff_email").val(data_response.email);
			$("#staff_department").val(data_response.department);
			$("#staff_designation").val(data_response.designation);	

			if(data_response.image != null ){
				if(image != "attachment/images/undefined_resize.jpg"){
					$("#image").attr('src',data_response.image);
				}
			}else{
				$("#image").attr('src','images/user128.png');
				console.log("Hey")
			}
			
			$("#supervisor_name").val(data_response.supervisor_name);
			$("#supervisor_em").val(data_response.supervisor_email);			
			$("#staff_contact").val(data_response.contact); 			
		}
	});
	
	
	
	//number item of borrowed item
	$.ajax({
		url: "db/number_borrowed_item.php",
		data: 'staff_id='+staff_id,
		type: "POST",
		datatype:"text",
		success:function(data){
			$("#no_borrowed_tg").text(data);			
		}
	});
	
	//number item of owned item
	$.ajax({
		url: "db/number_owned_item.php",
		data: 'staff_id='+staff_id,
		type: "POST",
		datatype:"text",
		success:function(data){
			$("#no_owned_tg").text(data);			
		}
	});

	//number item of owned master test gear
	$.ajax({
		url: "db/number_owned_master_item.php",
		data: 'staff_id='+staff_id,
		type: "POST",
		datatype:"text",
		success:function(data){
			$("#no_owned_master_tg").text(data);			
		}
	});
	
	
	
	$('#owned_tg').on('show.bs.modal', function (e) {
		
		var table = $('#ownedtg').DataTable({
			paging: true,
			searching: true,
			scrollX: true,
			language: {
				 loadingRecords: "<img src='images/loading.gif'/>"
			},
			ajax:'db/owned_tg_list.php?staff_id='+$("#staffid").val(),
			bDestroy: true,
			fnRowCallback: function( nRow, aData, iDisplayIndex ) {
				// var details_otg = $('td:eq(0)', nRow).html('<a href="" id="asset_tg_own">'+aData[0]+'</a>');
				// $(details_otg).click(function(){
				// 	alert(aData[6]);
				// 	$('#owned_tg').modal('hide');
				// 	var url = 'home.php#page7?mmeid=' + aData[0] + '&status=' + aData[6];
				// 	alert(url);
				// 	$("#asset_tg_own").attr("href", url);
				// });
				if(c_id == staff_id){
					//if(aData[6] != "PENDING VERIFICATION"){
					if(c_work != ""){
						//change verification date
						var change_verifdate = $('td:eq(5)', nRow).html('<button type="button" class="btn btn-sm">' + aData[5] + '</button>');
						$(change_verifdate).click(function() {
							$('#changeverifdate_modal').modal("show");
							$('#dateverifcalib').datepicker();
							$('#dateverifcalib').val(aData[5]);
							
							$("#save_verifdatetg").click(function() {
																
									$("#save_verifdatetg").html("Submitting....");
									$("#save_verifdatetg").attr("disabled", "disabled");
									
									var asset_no       = aData[0];
									var dateverifcalib = $("#dateverifcalib").val();
									
									
									var varDate = new Date(dateverifcalib);
									var today   = new Date();
									
								
									
									if(varDate <= today){
										var update_stat = "1";
									}
									
									
									$.ajax({
										url: "db/update_tg_verifcalibdate.php",
										data: "asset_no="+asset_no+"&dateverifcalib="+dateverifcalib+"&update_stat="+update_stat,
										type: "POST",
										datatype:"JSON",
										success:function(data_response){
											if(data_response.data == "SUCCESS"){
												$("#save_verifdatetg").html("Submit");
												$('.all_fields').val('');
												$('#changeverifdate_modal').modal("hide");
												$('#success_modal').modal("show");
												$('#success_modal').on('hidden.bs.modal', function () {
													$("#save_verifdatetg").html("Submit");
													$( "#save_verifdatetg" ).prop( "disabled", false );										
													$('#ownedtg').DataTable().ajax.reload();
												});
											}
											
										}
									});
								//}
								
							});
							
						});
					}
				
				//status
				
					if(aData[6] == "FAULT" || aData[6] == "FAULTY"){
						var status_modal = $('td:eq(6)', nRow).html('<button type="button" class="btn btn-sm">' + aData[6] + '</button>');
					
						$(status_modal).click(function() {
							$('#status_modal').modal("show");
							var o = new Option("PENDING VERIFICATION", "PENDING VERIFICATION");
							$(o).html("PENDING VERIFICATION");
							$("#statustg").append(o);
							
							$("#save_statustg").click(function() {
								$("#save_statustg").attr("disabled", "disabled");
								
								//alert(aData[0] + $("#statustg").val());
								var asset_no = aData[0];
								var status_tg = $("#statustg").val();
								
								
								$.ajax({
									url: "db/update_tg_status.php",
									data: "asset_no="+asset_no+"&status="+status_tg,
									type: "POST",
									datatype:"JSON",
									success:function(data_response){
										if(data_response.data == "SUCCESS"){
											$('.all_fields').val('');
											$('#status_modal').modal("hide");
											$('#success_modal').modal("show");
											$('#success_modal').on('hidden.bs.modal', function () {											
												$("#save_statustg").html("Submit");
												$( "#save_statustg" ).prop( "disabled", false );										
												$('#ownedtg').DataTable().ajax.reload();
											});
										}
										
									}
								});							
										
							});
							
							
						});
					
					}
					
					//Pending verification
					if(aData[6] == "PENDING VERIFICATION"){
						var status_modal = $('td:eq(6)', nRow).html('<button type="button" class="btn btn-sm">' + aData[6] + '</button>');
					
						$(status_modal).click(function() {
							
							$('#status_modal_pending').modal("show");
							
							$("#save_statustg_pending").click(function() {
								
								$("#save_statustg_pending").html("Submitting....");
								$("#save_statustg_pending").attr("disabled", "disabled");
								
								//alert(aData[0] + $("#statustg").val());
								var asset_no = aData[0];
								var status_tg = $("#statustg_pending").val();
								//console.log("Wei");
								
								$.ajax({
									url: "db/update_tg_status.php",
									data: "asset_no="+asset_no+"&status="+status_tg,
									type: "POST",
									datatype:"JSON",
									success:function(data_response){
										if(data_response.data == "SUCCESS"){
											$('.all_fields').val('');
											$('#status_modal_pending').modal("hide");
											$('#success_modal').modal("show");
											$('#success_modal').on('hidden.bs.modal', function () {											
												$("#save_statustg_pending").html("Submit");
												$( "#save_statustg_pending" ).prop( "disabled", false );										
												$('#ownedtg').DataTable().ajax.reload();
											});
										}
										
									}
								});							
										
							});
							
							
						});
					
					}
					
					
				}				
				return nRow;
			}
		});
	});
	

	$('#owned_master_tg').on('show.bs.modal', function (e) {
		
		var table2 = $('#ownedmastertg').DataTable({
			paging: true,
			searching: true,
			scrollX: true,
			language: {
				 loadingRecords: "<img src='images/loading.gif'/>"
			},
			ajax:'db/owned_master_tg_list.php?staff_id='+$("#staffid").val(),
			bDestroy: true,
			fnRowCallback: function( nRow, aData, iDisplayIndex ) {
				var change_calibdate = $('td:eq(4)', nRow).html('<button type="button" class="btn btn-sm">' + aData[4] + '</button>');
				$(change_calibdate).click(function(){
					$('#changecalibdate_modal').modal("show");
					$('#datecalib').datepicker();
					$('#datecalib').val(aData[4]);

					$("#save_calibdatetg").click(function() {
						$("#save_calibdatetg").attr("disabled", "disabled");

						var asset_no       = aData[0];
						var datecalib = $("#datecalib").val();

						var varDate = new Date(datecalib);
						var today   = new Date();
						

						$.ajax({
							url: "db/update_tg_calibdate.php",
							data: "asset_no="+asset_no+"&datecalib="+datecalib+"&update_stat="+aData[5],
							type: "POST",
							datatype:"JSON",
							success:function(data_response){
								if(data_response.data == "SUCCESS"){
									$("#save_calibdatetg").html("Submit");
									$('.all_fields').val('');
									$('#changecalibdate_modal').modal("hide");
									$('#success_modal').modal("show");
									$('#success_modal').on('hidden.bs.modal', function () {
										$("#save_calibdatetg").html("Submit");
										$( "#save_calibdatetg" ).prop( "disabled", false );										
										$('#ownedmastertg').DataTable().ajax.reload();
									});
								}
								
							}
						});


					});


				});

				//change fault status to another status
				if(aData[5] == "FAULT" || aData[5] == "FAULTY"){
					var status_modal = $('td:eq(5)', nRow).html('<button type="button" class="btn btn-sm">' + aData[5] + '</button>');
					
					$(status_modal).click(function() {
						$('#status_modal').modal("show");
						var o = new Option("PENDING CALIBRATION", "PENDING CALIBRATION");
						$(o).html("PENDING CALIBRATION");
						$("#statustg").append(o);

						$("#save_statustg").click(function() {
							$("#save_statustg").attr("disabled", "disabled");
							var asset_no = aData[0];
							var status_tg = $("#statustg").val();

						$.ajax({
							url: "db/update_mtg_status.php",
							data: "asset_no="+asset_no+"&status="+status_tg,
							type: "POST",
							datatype:"JSON",
							success:function(data_response){
								if(data_response.data == "SUCCESS"){
									$('.all_fields').val('');
									$('#status_modal').modal("hide");
									$('#success_modal').modal("show");
									$('#success_modal').on('hidden.bs.modal', function () {											
										$("#save_statustg").html("Submit");
										$( "#save_statustg" ).prop( "disabled", false );										
										$('#ownedmastertg').DataTable().ajax.reload();
									});
								}
								
							}
						});


						})
						
					});


				}
							

			}
		})
	});

	
	$('#borrowed_tg').on('show.bs.modal', function (e) {
		
		var table = $('#borrowedtg').DataTable({
			paging: true,
			searching: true,
			scrollX: true,
			language: {
				 loadingRecords: "<img src='images/loading.gif'/>"
			},
			ajax:'db/borrowed_tg_list.php?staff_id='+$("#staffid").val(),
			bDestroy: true,
			fnRowCallback: function( nRow, aData, iDisplayIndex ) {
				/*$('td:eq(1)', nRow).html('<a href="home.php#page7?mmeid=' + aData[1] + '">' +
					aData[1] + '</a>');
				var details_tg = $('td:eq(0)', nRow).html('<a href="home.php#page7?mmeid=' + aData[0] + '&status=UNAVAILABLE">' +
				aData[0] + '</a>');	
				*/
				var details_tg = $('td:eq(0)', nRow).html('<a href="home.php#page7?mmeid=' + aData[0] + '&status=UNAVAILABLE" id="asset_tg_borrow">'+aData[0]+'</a>');
				//http://10.21.11.64/Apps/eCOMME2/home.php#page7?mmeid=90000007052&status=UNAVAILABLE
				$(details_tg).click(function(){
					$('#borrowed_tg').modal('hide');
					var url = 'home.php#page7?mmeid=' + aData[0] + '&status=UNAVAILABLE';
					$("#asset_tg_borrow").attr("href", url);
				});
				
				// var owner_tg = $('td:eq(4)', nRow).html('<a href="home.php#page8?staff_id=' + aData[3] + '">' +
				// aData[4] + '</a>');	

				var owner_tg = $('td:eq(3)', nRow).html('<a href="home.php#page8?staff_id=' + aData[3] + '" id="owner_tg_borrow">'+aData[3]+'</a>');
				$(owner_tg).click(function(){
					$('#borrowed_tg').modal('hide');
					var url = 'home.php#page8?staff_id=' + aData[3];
					$("#owner_tg_borrow").attr("href", url);
				});
				return nRow;
			}
		});
	});
	
	
	$("#edit_profile").click(function(){
		$(".edit").attr("readonly", false); 
		$(".edit").addClass("editable"); 
		$(".first_edit").focus(); 
		$(".upload-image").show();
		$("#edit_profile").fadeOut( "fast", function() {
			$("#submit_profile").fadeIn("fast"); 			
			$("#cancel_profile").fadeIn("fast"); 
		});		
	});
	
	$("#edit_profile").tooltip();
	$("#submit_profile").tooltip();	
	$("#cancel_profile").tooltip();
	
	
	$("#submit_profile").click(function(){
		//var image = split ("/",$("#myimage").val());
		var uploaded_image = $("#uploadimage").val();
		var image = uploaded_image.split("\\");
		var img = "attachment/images/"+image[2];

		
		//alert("Click!");
		var value_send =  "staff_id=" + parts[1] +
							"&email=" + $("#staff_email").val() +
							"&contact_number=" + $("#staff_contact").val() +	
							"&supervisor_email=" + $("#supervisor_em").val() +	
							"&image=" + img;


		$.ajax({
			url: "db/update_profile.php",
			data: value_send,
			type: "POST",
			datatype:"JSON",
			//datatype:"html",
			success:function(data){
				if (data){
					var result = JSON.stringify(data).replace(/\"/g, "");
					//alert(result);
					var n = result.indexOf("SUCCESS");
					if(n>=0){
						//location.reload();	
						//return false;
					}
					// e.preventDefault();
					
					var formData = new FormData();
					formData.append('file', $('input[type=file]')[0].files[0]);
					
					//upload image in serverSide
					jQuery.ajax({
						url: "db/upload_profile_picture.php", // Url to which the request is send
						type: "POST",             // Type of request to be send, called as method
						data: formData, // Data sent to server, a set of key/value pairs (i.e. form fields and values)
						contentType: false,       // The content type used when sending data to the server.
						cache: false,             // To unable request pages to be cached
						processData:false,        // To send DOMDocument or non processed data file it is set to false
						success: function(data)   // A function to be called if request succeeds
						{
							location.reload();

						}
						
					});					
					
				}
			},
			error:function (){}
		});								
	});
	
	$("#cancel_profile").click(function(){
		location.reload();
	});

	//console.log($("#staff_id_session").text())
	//console.log($("#staffid").val())
	if($("#staff_id_session").text() == $("#staffid").val()){
		$('#my_tg').removeClass("d-none");
		$('#my_mtg').removeClass("d-none");
	}
	//List of owned TG
	var table = $('#own_asset_mme_list').DataTable( {
		ajax:'db/own_mme_asset_list.php',
		fnRowCallback: function( nRow, aData, iDisplayIndex ) {	
			var today   = new Date();
			var staff_id = $("#staff_id_session").text();
			var workgroup = $("#workgroup_session").text();
			
			var cost_center = aData[1];
			var asset_id = aData[2];
			var serial_no = aData[3];
			var inventory_name = aData[4];
			var health_check = aData[5];
			var verif_calib = aData[6];
			var status = aData[7];	
			var date_health_check = new Date(health_check);
			var date_verif_calib = new Date(verif_calib);

			if(date_verif_calib <= today &&  status == "AVAILABLE") {
					
				//update status = 'pending verification '
				$.ajax({
					url: "db/update_status_pending_verification.php",
					data:"asset_id="+asset_id,
					type: "POST",
					datatype:"JSON",
					success:function(data_response){						
					}
				});
			}

			$('td:eq(2)', nRow).html('<a href="home.php#page7?mmeid=' + asset_id + '&status=' + status + '">'+ asset_id +'</a>');
			
			if (date_health_check <= today && (status == "AVAILABLE" || status == "SPARE")){
				var health_check_btn = $('td:eq(5)', nRow).html('<button type="button" class="btn btn-sm" style="background-color:red">' + health_check + '</button>');
			}
			
			if (date_verif_calib <= today && (status  == "PENDING VERIFICATION" || status  == "IN PROGRESS VERIFICATION" || status == "IN PROGRESS CALIBRATION")){
				var verif_calib_btn = $('td:eq(6)', nRow).html('<button type="button" class="btn btn-sm" style="background-color:red">' + verif_calib + '</button>');
			}

			// click button health check
			$(health_check_btn).click(function() {
				health_check_action(cost_center,asset_id,serial_no,inventory_name,health_check,verif_calib,status,staff_id,today);
			});			

			$(verif_calib_btn).click(function() {
				verification_calib_action(cost_center,asset_id,serial_no,inventory_name,health_check,verif_calib,status,staff_id,today);
			});

			//Refresh 
			$('.modal').on('hidden.bs.modal', function () {
				$("#loading_hc_icon").hide();
				$('#save_health_check').prop('disabled', false);
				$("#loading_verif_icon").hide();
				$('#save_verif').prop('disabled', false);
				$("#loading_calib_icon").hide();
				$('#save_calib').prop('disabled', false);
				$(this)
				.find("input,textarea,select")
				   .val('')
				   .end()
				.find("input[type=checkbox], input[type=radio]")
				   .prop("checked", "")
				   .end();
			 })

			
		}		
	});

	// click button save health check details
	$("#save_health_check").click(function() {
		$("#loading_hc_icon").show();
		$('#save_health_check').prop('disabled', true);

		var asset_id = $("#mmeasset_modal").val();
		var inventory_name = $("#inventoryname_modal").val();
		var health_check = $("#healthcheckdate_modal").val();
		var status = $("#status_modal_hc").val();
		var staff_id = $("#performedby_modal").val();
		var new_stat = $("#new_stat_modal").val();
		var remark = $("#remark_healthcheck").val();
		var aros_id = $("#arosid_modal").val();
		var today   = new Date();

		if(new_stat != "" && remark != ""){
			if (new_stat == "AVAILABLE" ||new_stat == "SPARE"||new_stat == "FAULTY" ){
				var proceed = "1";
			}
			else {
				if( aros_id != "" ){
					var proceed = "1";
				}
			}
		}

		// Proceed to save details
		if (proceed == "1"){
			var date = today.toISOString().slice(0,10);
			var n = 1;
			var month_after = n.months().fromNow();
			var target_date = month_after.toISOString().slice(0,10);

			var value_send = "asset_id=" + asset_id +
							"&inventory_name=" + inventory_name +
							"&health_check_date=" + health_check +
							"&status=" + status +
							"&new_status=" + new_stat +
							"&performed_by=" + staff_id +
							"&remarks=" + remark +
							"&aros_id=" + aros_id +
							"&performed_date=" + date +
							"&target_date=" + target_date +
							"&tg_owner=" + staff_id;


			$.ajax({
				url:"db/update_health_check.php",
				data:value_send,
				type: "POST",
				datatype:"JSON",
				success:function(data_response){
					console.log(data_response);	
					if(data_response.data == "SUCCESS"){
						
						var supervisor_name = data_response.supervisor_name
						var supervisor_email = data_response.supervisor_email
						var owner_name = data_response.owner_name
						var owner_email = data_response.owner_email
						var staff_email = data_response.staff_email
						var staff_name = data_response.staff_name
						var email_task = "MONTHLY HEALTH CHECK"
						
						send_email(supervisor_name,supervisor_email,owner_name,owner_email,staff_email,staff_name,email_task,asset_id,inventory_name,staff_id,new_stat);


					} else {
						$('#error_modal').modal("show");

					}
				},
				error: function (request, status, error) {
					$('#error_modal').modal("show");

				}
			});
			
		} else {
			$('#error_modal').modal("show");

		}
		
	});

	$("#save_verif").on("click",function(){	
		$("#loading_verif_icon").show();
		$('#save_verif').prop('disabled', true);
	
		var status_verif = $("#verif_stat_modal").val();
		var verif_filename = $("#myfile_verif").val();
		var mme_cert_numb = $("#mmecertnum").val();
		var nextverifdate = $("#nextverifdate").val();
		var aros_id_verif = $("#arosid_verif_modal").val();
		var result_verif = $("#result_verif").val();
		var verif_date = $("#verifdate").val();
		var verif_calib = $("#verifduedate").val();
		var verifier = $("#verifier").val();
		var asset_id = $("#mmeassetverif").val();		
		var tg_owner = $("#mmeasset_tgowner").val();
		var inventory_name = $("#inventorynameverif").val();
		var update_in_progress = $("#update_in_progress").val();
		var today   = new Date();
		var new_today_date = today.toISOString().slice(0,10);
		var staff_id = $("#staff_id_session").text();

		

		if(update_in_progress == "1"){

			var remark = $("#remarksverif").val();
			if(remark != ""){
				var value_send = "asset_id=" + asset_id +
								"&performed_by=" + staff_id +
								"&inventory_name=" + inventory_name +
								"&result=" + result_verif +
								"&tg_owner=" + staff_id +
								"&today_date=" + verif_date +
								"&remark=" + remark +
								"&activity=VERIFICATION";
				
			
				$.ajax({
					url: "db/update_inprogress_verif_calib.php",
					data:value_send,
					type: "POST",
					datatype:"JSON",
					success:function(data_response){
						if(data_response.data == "SUCCESS"){
							var supervisor_name = data_response.supervisor_name
							var supervisor_email = data_response.supervisor_email
							var owner_name = data_response.owner_name
							var owner_email = data_response.owner_email
							var staff_email = data_response.staff_email
							var staff_name = data_response.staff_name
							var email_task = "IN PROGRESS VERIFICATION"
							var new_stat = email_task
							
							send_email(supervisor_name,supervisor_email,owner_name,owner_email,staff_email,staff_name,email_task,asset_id,inventory_name,staff_id,new_stat);
						}
					}
				})
			} else {
				$('#error_modal').modal("show");

			}
		}
		else {
			if(status_verif != "" && verif_filename != "" && mme_cert_numb != "" && result_verif != ""){				
				if (status_verif == "AVAILABLE" ||status_verif == "SPARE" || status_verif == "FAULTY" ){
					var proceed = "1";
				}
				else {
					if( aros_id_verif != "" ){
						var proceed = "1";	
					}
				}
			}

			if(proceed == "1"){
				var uploaded_att = $("#myfile_verif").val();
				var att = uploaded_att.split("\\");
				var attachment = "attachment/result/"+att[2];
				var asset_id_filename = asset_id.replace("/", "_");
				var attachment = "attachment/result/"+asset_id_filename+"_"+new_today_date+"_verification.pdf";

				var value_send = "asset_id=" + asset_id +
							"&performed_by=" + staff_id +
							"&inventory_name=" + inventory_name +
							"&verifdate=" + new_today_date +
							"&verifduedate=" + verif_calib +
							"&verifinterval=" + $("#verifinterval").val() +
							"&nextverifdate=" + nextverifdate +
							"&justification=" + $("#justification_verif").val() +
							"&aros_id=" + aros_id_verif +
							"&status_verif=" + status_verif +
							"&attachment=" + attachment +
							"&mmecertnum=" + mme_cert_numb +
							"&result_verif=" + result_verif +
							"&tg_owner=" + staff_id +
							"&verifier=" + verifier +
							"&activity=VERIFICATION";

				$.ajax({
					url: "db/update_verification.php",
					data:value_send,
					type: "POST",
					datatype:"JSON",
					success:function(data_response){
						if(data_response.data == "SUCCESS"){

							//upload file
							var asset_id_filename = asset_id.replace("/", "_");
							var filename = asset_id_filename+"_"+new_today_date+"_verification.pdf";
							var filenameD = $('#myfile_verif').val().split('\\').pop();
							var fileD = $('#myfile_verif').prop('files')[0];
							var formData = new FormData();
							formData.append('file', fileD);
							formData.append('filename', filename);
							
							//upload file in serverSide
							$.ajax({
								url: "db/upload_result_file.php",
								type: "POST",
								data: formData,
								contentType: false,
								cache: false,
								processData:false,
								success: function(data){
									console.log(data)
									if (data_response.data == "SUCCESS"){
										var supervisor_name = data_response.supervisor_name
										var supervisor_email = data_response.supervisor_email
										var owner_name = data_response.owner_name
										var owner_email = data_response.owner_email
										var staff_email = data_response.staff_email
										var staff_name = data_response.staff_name
										var email_task = "VERIFICATION"
										var new_stat = result_verif + " - " + status_verif
										
										send_email(supervisor_name,supervisor_email,owner_name,owner_email,staff_email,staff_name,email_task,asset_id,inventory_name,staff_id,new_stat);
									}
									else {
										$('#error_modal').modal("show");


									}
								}
							});
						}
						else {
							$('#error_modal').modal("show");

						}
					},
					error: function (request, status, error) {
						$('#error_modal').modal("show");

					}
				});
			}
			else {
				$('#error_modal').modal("show");

			}
		}			
	});

	$("#save_calib").on("click",function(){
		$("#loading_calib_icon").show();
		$('#save_calib').prop('disabled', true);

		var asset_id = $("#mmeassetcalib").val();	
		var staff_id = $("#staff_id_session").text();
		var inventory_name = $("#inventorynamecalib").val();		
		var status_calib = $("#calib_stat_modal").val();
		var calib_filename = $("#myfile_calib").val();
		var mme_cert_numb = $("#mmecertnum_calib").val();
		var nextcalibdate = $("#nextcalibdate").val();
		var aros_id_calib = $("#arosid_calib_modal").val();
		var result_calib = $("#result_calib").val();
		var verif_calib = $("#calibduedate").val();
		var calib_date = $("#calibdate").val();
		var calibrator = $("#calibrator").val();		
		var update_in_progress = $("#update_in_progress_calib").val();
		var today   = new Date();
		var new_today_date = today.toISOString().slice(0,10);

		

		if(update_in_progress == "1"){

			var remark = $("#remarkscalib").val();
			if(remark != ""){
				var value_send = "asset_id=" + asset_id +
							"&performed_by=" + staff_id +
							"&inventory_name=" + inventory_name +
							"&result=" + result_calib +
							"&tg_owner=" + staff_id +
							"&today_date=" + calib_date +
							"&remark=" + remark +
							"&activity=CALIBRATION";
			

				$.ajax({
					url: "db/update_inprogress_verif_calib.php",
					data:value_send,
					type: "POST",
					datatype:"JSON",
					success:function(data_response){
						if(data_response.data == "SUCCESS"){
							var supervisor_name = data_response.supervisor_name
							var supervisor_email = data_response.supervisor_email
							var owner_name = data_response.owner_name
							var owner_email = data_response.owner_email
							var staff_email = data_response.staff_email
							var staff_name = data_response.staff_name
							var email_task = "IN PROGRESS CALIBRATION"
							var new_stat = email_task
							
							send_email(supervisor_name,supervisor_email,owner_name,owner_email,staff_email,staff_name,email_task,asset_id,inventory_name,staff_id,new_stat);
						}
					}
				})
			} else {
				$('#error_modal').modal("show");
		
			}
		}
		else {
			if(status_calib != "" && calib_filename != "" && mme_cert_numb != "" && result_calib != ""){
				if (status_calib == "AVAILABLE" ||status_calib == "SPARE" || status_calib == "FAULTY" ){
					var proceed = "1";
				}
				else {
					if( aros_id_calib != "" ){
						var proceed = "1";	
					}
				}
			}

			if (proceed == "1"){
				var uploaded_att = $("#myfile_calib").val();
				var att = uploaded_att.split("\\");
				var attachment = "attachment/result/"+att[2];
				var asset_id_filename = asset_id.replace("/", "_");
				var attachment = "attachment/result/"+asset_id_filename+"_"+new_today_date+"_calibration.pdf";

				var value_send = "asset_id=" + asset_id +
							"&performed_by=" + staff_id +
							"&inventory_name=" + inventory_name +
							"&calibdate=" + new_today_date +
							"&calibduedate=" + verif_calib +
							"&calibinterval=" + $("#calibinterval").val() +
							"&nextcalibdate=" + nextcalibdate +
							"&justification=" + $("#justification_calib").val() +
							"&aros_id=" + aros_id_calib +
							"&status_calib=" + status_calib +
							"&attachment=" + attachment +
							"&mmecertnum=" + mme_cert_numb +
							"&result_calib=" + result_calib +
							"&tg_owner=" + staff_id +
							"&calibrator=" + calibrator +
							"&activity=CALIBRATION";

				$.ajax({
					url: "db/update_calibration.php",
					data:value_send,
					type: "POST",
					datatype:"JSON",
					success:function(data_response){
						if(data_response.data == "SUCCESS"){
							//upload file
							var asset_id_filename = asset_id.replace("/", "_");
							var filename = asset_id_filename+"_"+new_today_date+"_calibration.pdf";
							var filenameD = $('#myfile_calib').val().split('\\').pop();
							var fileD = $('#myfile_calib').prop('files')[0];
							var formData = new FormData();
							formData.append('file', fileD);
							formData.append('filename', filename);
							//upload file in serverSide
							$.ajax({
								url: "db/upload_result_file.php",
								type: "POST",
								data: formData,
								contentType: false,
								cache: false,
								processData:false,
								success: function(data){
									console.log(data)
									if (data_response.data == "SUCCESS"){
										var supervisor_name = data_response.supervisor_name
										var supervisor_email = data_response.supervisor_email
										var owner_name = data_response.owner_name
										var owner_email = data_response.owner_email
										var staff_email = data_response.staff_email
										var staff_name = data_response.staff_name
										var email_task = "CALIBRATION"
										var new_stat = result_calib + " - " + status_calib
										
										send_email(supervisor_name,supervisor_email,owner_name,owner_email,staff_email,staff_name,email_task,asset_id,inventory_name,staff_id,new_stat);
									}
									else {
										$('#error_modal').modal("show");
										$("#error").on("click",function(){
											$('#verif_calib').modal('hide')
										});

									}
								}
							});
						}
						else {
							$('#error_modal').modal("show");

						}
					},
					error: function (request, status, error) {
						$('#error_modal').modal("show");

					}
				})
			}
			else {
				$('#error_modal').modal("show");

			}
		}
	});

    //List of owned Master TG
	var table = $('#own_master_asset_mme_list').DataTable({
		ajax:'db/own_master_mme_asset_list.php',
		fnRowCallback: function( nRow, aData, iDisplayIndex ) {	
			var today   = new Date();
			var staff_id = $("#staff_id_session").text();
			var workgroup = $("#workgroup_session").text();

			var vc = aData[1];			
			var cost_center = aData[2];
			var master_mme_asset_id = aData[3];
			var master_mme_name = aData[4];
			var serial_number = aData[5];
			var health_check = aData[6];
			var calib = aData[7];			
			var status = aData[8];
			var vc_location = aData[9];
			var date_health_check = new Date(health_check);
			var date_calib = new Date(calib);

			if(date_calib <= today &&  status == "AVAILABLE") {
					
				//update status = 'pending verification '
				$.ajax({
					url: "db/update_status_pending_calibration.php",
					data:"asset_id="+master_mme_asset_id,
					type: "POST",
					datatype:"JSON",
					success:function(data_response){						
					}
				});
			}

			$('td:eq(3)', nRow).html('<a href="home.php#page6?master_mmeid=' + master_mme_asset_id + '&status=' + status + '">'+ master_mme_asset_id +'</a>');
			
			if (date_health_check <= today && (status == "AVAILABLE" || status == "SPARE")){
				var health_check_btn = $('td:eq(6)', nRow).html('<button type="button" class="btn btn-sm" style="background-color:red">' + health_check + '</button>');
			}
			
			if (date_calib <= today && (status  == "PENDING CALIBRATION" || status  == "IN PROGRESS CALIBRATION" )){
				var calib_btn = $('td:eq(7)', nRow).html('<button type="button" class="btn btn-sm" style="background-color:red">' + calib + '</button>');
			}

			// click button health check
			$(health_check_btn).click(function() {
				master_health_check_action(vc,cost_center,master_mme_asset_id,master_mme_name,serial_number,health_check,staff_id,vc_location,status,today);
			});

			$(calib_btn).click(function() {
				calibration_action(vc,cost_center,master_mme_asset_id,master_mme_name,serial_number,calib,staff_id,vc_location,today);
			});



			//Refresh Modal
			$('.modal').on('hidden.bs.modal', function () {
				$("#calib_loading_icon").hide();
				$('#master_mme_save_calib').prop('disabled', false);
				$("#loading_icon").hide();
				$('#master_mme_save_health_check').prop('disabled', false);
				$(this)
				.find("input,textarea,select")
				   .val('')
				   .end()
				.find("input[type=checkbox], input[type=radio], button")
				   .prop("checked", "")
				   .end();
			})



		}
	})
	
	// click button save health check details
	$("#master_mme_save_health_check").on("click",function(){
		$("#loading_icon").show()
		$('#master_mme_save_health_check').prop('disabled', true);
		
		var master_mme_asset_id = $("#master_mmeasset_modal").val();
		var master_mme_name = $("#master_mme_inventoryname_modal").val();
		var health_check = $("#master_mme_healthcheckdate_modal").val();
		var status = $("#master_mme_status_modal").val();
		var staff_id = $("#master_mme_performedby_modal").val();
		var tg_owner = $("#master_mme_tgowner_modal").val();
		var new_stat = $("#master_mme_new_stat_modal").val();
		var remark = $("#master_mme_remark_healthcheck").val();
		var aros_id = $("#master_mme_arosid_modal").val();

		if(new_stat != "" && remark != ""){
			if (new_stat == "AVAILABLE" ||new_stat == "SPARE"||new_stat == "FAULTY" ){
				var proceed = "1";
			}
			else {
				if( aros_id != "" ){
					var proceed = "1";
				}
			}
		}

		if (proceed == "1"){
			var date = today.toISOString().slice(0,10);
			var n = 1;
			var month_after = n.months().fromNow();
			var target_date = month_after.toISOString().slice(0,10);

			var value_send = "asset_id=" + master_mme_asset_id +
							"&inventory_name=" + master_mme_name +
							"&health_check_date=" + health_check +
							"&status=" + status +
							"&new_status=" + new_stat +
							"&performed_by=" + staff_id +
							"&remarks=" + remark +
							"&aros_id=" + aros_id +
							"&performed_date=" + date +
							"&target_date=" + target_date +
							"&tg_owner=" + staff_id;

			$.ajax({
				url:"db/master_mme_update_health_check.php",
				data:value_send,
				type: "POST",
				datatype:"JSON",
				success:function(data_response){
					console.log(data_response);	
					if(data_response.data == "SUCCESS"){
						
						var supervisor_name = data_response.supervisor_name
						var supervisor_email = data_response.supervisor_email
						var owner_name = data_response.owner_name
						var owner_email = data_response.owner_email
						var staff_email = data_response.staff_email
						var staff_name = data_response.staff_name
						var email_task = "MASTER MONTHLY HEALTH CHECK"
						
						send_email(supervisor_name,supervisor_email,owner_name,owner_email,staff_email,staff_name,email_task,master_mme_asset_id,master_mme_name,staff_id,new_stat);

					} else {
						$('#error_modal').modal("show");
					}
				},
				error: function (request, status, error) {
					$('#error_modal').modal("show");
				}
			});


		}
		else {
			$('#error_modal').modal("show");
		}

	});	

	//master calibration
	$("#master_mme_save_calib").on("click",function(){
		$("#calib_loading_icon").show()
		$('#master_mme_save_calib').prop('disabled', true);

		var today_date   = new Date()
	    var new_today_date = today_date.toISOString().slice(0,10);

		var master_mme_asset_id = $("#master_mme_assetcalib").val();
		var staff_id = $("#staff_id_session").text();
		var master_mme_name = $("#master_mme_inventorynamecalib").val();
		var tg_owner = $("#master_mme_tgownercalib").val();
		var status_calib = $("#master_mme_calib_stat_modal").val();
		var calib_filename = $("#master_mme_myfile_calib").val();
		var mme_cert_numb = $("#master_mme_mmecertnum_calib").val();
		var nextcalibdate = $("#master_mme_nextcalibdate").val();
		var aros_id_calib = $("#master_mme_arosid_calib_modal").val();
		var result_calib = $("#master_mme_result_calib").val();
		var calib_date = $("#master_mme_calibdate").val();
		var calibrator = $("#master_mme_calibrator").val();
		var calib = $("#master_mme_calibduedate").val();
		var update_in_progress = $('#update_in_progress_master_calib').val();


		if(update_in_progress == "1"){
			var remark = $("#masterremarkscalib").val();
			if(remark != ""){
				var value_send = "asset_id=" + master_mme_asset_id +
				"&performed_by=" + staff_id +
				"&inventory_name=" + master_mme_name +
				"&result=" + result_calib +
				"&tg_owner=" + staff_id +
				"&today_date=" + calib_date +
				"&calibrator=" + calibrator +
				"&remark=" + remark +
				"&activity=CALIBRATION";


				$.ajax({
					url: "db/master_mme_update_inprogress_calib.php",
					data:value_send,
					type: "POST",
					datatype:"JSON",
					success:function(data_response){
						if(data_response.data == "SUCCESS"){
							var supervisor_name = data_response.supervisor_name
							var supervisor_email = data_response.supervisor_email
							var owner_name = data_response.owner_name
							var owner_email = data_response.owner_email
							var staff_email = data_response.staff_email
							var staff_name = data_response.staff_name
							var email_task = "IN PROGRESS MASTER CALIBRATION"
							var new_stat = email_task							
							send_email(supervisor_name,supervisor_email,owner_name,owner_email,staff_email,staff_name,email_task,master_mme_asset_id,master_mme_name,staff_id,new_stat);
						}
					}
				})
			} else {
				$('#error_modal').modal("show");
			}
		}
		else {
			if(status_calib != "" && calib_filename != "" && mme_cert_numb != "" && result_calib != "" && calibrator!= ""){
				if (status_calib == "AVAILABLE" ||status_calib == "SPARE" || status_calib == "FAULTY" ){
					var proceed = "1";
				}
				else {
					if( aros_id_calib != "" ){
						var proceed = "1";	
					}
				}
			}else if($("#master_mme_result_calib").val() == "FAIL" && calib_filename == "" && mme_cert_numb == "") {
				var proceed = "2";
			}

			if (proceed == "1"){
				var uploaded_att = $("#master_mme_myfile_calib").val();
				var att = uploaded_att.split("\\");
				var attachment = "attachment/result/"+att[2];
				var master_mme_asset_id_filename = master_mme_asset_id.replace("/", "_");				
				var attachment = "attachment/result/"+master_mme_asset_id_filename+"_"+new_today_date+"_calibration.pdf";

				var value_send = "asset_id=" + master_mme_asset_id +
							"&performed_by=" + staff_id +
							"&inventory_name=" + master_mme_name +
							"&calibdate=" + new_today_date +
							"&calibduedate=" + calib +
							"&calibinterval=" + $("#master_mme_calibinterval").val() +
							"&nextcalibdate=" + nextcalibdate +
							"&justification=" + $("#master_mme_justification_calib").val() +
							"&aros_id=" + aros_id_calib +
							"&status_calib=" + status_calib +
							"&attachment=" + attachment +
							"&mmecertnum=" + mme_cert_numb +
							"&result_calib=" + result_calib +
							"&tg_owner=" + staff_id +
							"&calibrator=" + calibrator +
							"&activity=MASTER_CALIBRATION";

				$.ajax({
					url: "db/master_mme_update_calibration.php",
					data:value_send,
					type: "POST",
					datatype:"JSON",
					success:function(data_response){
						if(data_response.data == "SUCCESS"){
							//upload file
							var master_mme_asset_id_filename = master_mme_asset_id.replace("/", "_");
							var filename = master_mme_asset_id_filename+"_"+new_today_date+"_calibration.pdf";
							var filenameD = $('#master_mme_myfile_calib').val().split('\\').pop();
							var fileD = $('#master_mme_myfile_calib').prop('files')[0];
							var formData = new FormData();
							formData.append('file', fileD);
							formData.append('filename', filename);
							//upload file in serverSide
							$.ajax({
								url: "db/upload_result_file.php",
								type: "POST",
								data: formData,
								contentType: false,
								cache: false,
								processData:false,
								success: function(data){
									console.log(data)
									if (data_response.data == "SUCCESS"){
										var supervisor_name = data_response.supervisor_name
										var supervisor_email = data_response.supervisor_email
										var owner_name = data_response.owner_name
										var owner_email = data_response.owner_email
										var staff_email = data_response.staff_email
										var staff_name = data_response.staff_name
										var email_task = "MASTER CALIBRATION"
										var new_stat = result_calib + " - " + status_calib
										
										send_email(supervisor_name,supervisor_email,owner_name,owner_email,staff_email,staff_name,email_task,master_mme_asset_id,master_mme_name,staff_id,new_stat);
									}
									else {
										$('#error_modal').modal("show");

									}
								}
							});
						}
						else {
							$('#error_modal').modal("show");

						}
					},
					error: function (request, status, error) {
						$('#error_modal').modal("show");

					}
				})
			}
			else if (proceed == "2"){
				// var uploaded_att = $("#master_mme_myfile_calib").val();
				// var att = uploaded_att.split("\\");
				// var attachment = "attachment/result/"+att[2];
				// var attachment = "attachment/result/"+master_mme_asset_id+"_"+today+"_calibration.pdf";
				var attachment = "";
				var value_send = "asset_id=" + master_mme_asset_id +
							"&performed_by=" + staff_id +
							"&inventory_name=" + master_mme_name +
							"&calibdate=" + today +
							"&calibduedate=" + calib +
							"&calibinterval=" + $("#master_mme_calibinterval").val() +
							"&nextcalibdate=" + nextcalibdate +
							"&justification=" + $("#master_mme_justification_calib").val() +
							"&aros_id=" + aros_id_calib +
							"&status_calib=" + status_calib +
							"&attachment=" + attachment +
							"&mmecertnum=" + mme_cert_numb +
							"&result_calib=" + result_calib +
							"&tg_owner=" + tg_owner +
							"&calibrator=" + calibrator +
							"&activity=MASTER_CALIBRATION";

				$.ajax({
					url: "db/master_mme_update_calibration.php",
					data:value_send,
					type: "POST",
					datatype:"JSON",
					success:function(data_response){
						if(data_response.data == "SUCCESS"){
							var supervisor_name = data_response.supervisor_name
							var supervisor_email = data_response.supervisor_email
							var owner_name = data_response.owner_name
							var owner_email = data_response.owner_email
							var staff_email = data_response.staff_email
							var staff_name = data_response.staff_name
							var email_task = "MASTER CALIBRATION"
							var new_stat = result_calib + " - " + status_calib
							
							send_email(supervisor_name,supervisor_email,owner_name,owner_email,staff_email,staff_name,email_task,master_mme_asset_id,master_mme_name,staff_id,new_stat);
						}
						else {
							$('#error_modal').modal("show");

						}
					},
					error: function (request, status, error) {
						$('#error_modal').modal("show");

					}
				})
			}
			else {
				$('#error_modal').modal("show");

			}
		}

	})

	
	
	
	
	$('[data-toggle=popover]').popover({
		content: $('#eqp1ref').html(),
		html: true,
		trigger: 'focus'
	 }).click(function() {
		$(this).popover('show');
	 });


	 $("button[name='closebtn']").on("click",function(){
		$('form')[0].reset();
	 });

	 $('.modal').on('hidden.bs.modal', function () {

		$("#loading_hc_icon").hide();
		$("#loading_verif_icon").hide();
		$("#loading_calib_icon").hide();

		$('#save_health_check').prop('disabled', false);
		$('#save_verif').prop('disabled', false);
		$('#save_calib').prop('disabled', false);

		$("#remarks_verif").hide();		
		$("#remarks_calib").hide();
		$("#master_mme_remarks_calib").hide();		
		
		$("#verif_result_input_filled").hide();
		$("#calib_result_input_filled").hide();
		$("#master_mme_calib_result_input_filled").hide();
		
	 });



	 // New enhancement - User request to add module regional in eCOMME
	var table_master_list_regional_table = $('#master_list_regional_table').DataTable({
		scrollX: true,
		select: true,
		dom: 'lBfrtip',
		buttons: [
			{
				extend:'excelHtml5',
				className: 'red'
			},
            {
                text: 'New',
                action: function ( e, dt, node, config ) {
                    

                	$("#bTnEditRegional").addClass("d-none");
                	$("#bTnSaveRegional").removeClass("d-none");
                    $("#regional_modal").modal("show");
                    $("#formRegional")[0].reset()

                }
            },
            {
                text: 'Edit',
                action: function ( e, dt, node, config ) {

                	$("#formRegional")[0].reset()

			        if (table_master_list_regional_table.row('.selected').id() === undefined) {

			            $("#error_modal").modal("show");
			            $("#error_msg_header").text("Error!");
			            $("#error_msg").html('No circuit is selected, click on any circuit.')

			        } else {

	                	$("#bTnSaveRegional").addClass("d-none");
	                	$("#bTnEditRegional").removeClass("d-none");
	                    $("#regional_modal").modal("show");

	                    $("#regional_name").val(table_master_list_regional_table.row('.selected').data().col_regional_name)
    	                $("#division_lob").val(table_master_list_regional_table.row('.selected').data().col_division_lob)
			        }

                }
            },
            {
                text: 'Delete',
                action: function ( e, dt, node, config ) {

			        if (table_master_list_regional_table.row('.selected').id() === undefined) {

			            $("#error_modal").modal("show");
			            $("#error_msg_header").text("Error!");
			            $("#error_msg").html('No circuit is selected, click on any circuit.')

			        } else {

			        	console.log(table_master_list_regional_table.row('.selected').id())
						$.ajax({
							url: "db/regional.php",
							dataType: "json",
							type: "POST",
							data: "function=delete&rowid="+table_master_list_regional_table.row('.selected').id(),
							success: function(response) {

								// console.log(response)
								// return false;
								if (response.data == 'failed') {
									$('#error_modal').modal("show");
									$('#error_msg').text(response.message)
								} else {
									$('#success_msg').text(response.message)
									$('#success_modal').modal("show");
									$('#regional_modal').modal("hide");
								}

								table_master_list_regional_table.ajax.reload();

							}
						})

			        }
                    
                }
            }
		],
		destroy: true,
		lengthChange: false,
		language: {
			loadingRecords: "<img src='images/loading.gif'/>"
		},
		ajax: {
			"url": "db/regional.php",
			"type": "POST",
			"data":{
				'function':'display'
			},
			"dataSrc": 'regional_list_data'
			// success: function (response) {
			// 	console.log(response);
			// }
		},
        "columns": [
        	// { 'data': 'checkbox' },
            { 'data': 'col_regional_name' },
            { 'data': 'col_division_lob' },
            { 'data': 'col_created_by' },
            { 'data': 'col_updated_by' },
            { 'data': 'col_created_at' },
            { 'data': 'col_updated_at' },
            // { 'data': 'col_action' }
            // { 'data': 'row_id' }
        ],
		// fnRowCallback: function( nRow, aData, iDisplayIndex ) {	

			// var edit_verifier = $('td:eq(6)', nRow).html('<button type="button" class="btn btn-sm">' + "Edit"+ '</button>');
			// var delete_verifier = $('td:eq(1)', nRow).html('<button type="button" style="background-color:red" class="btn btn-sm">' + "Delete"+ '</button>');



		// // 	console.log(aData)

		// // 	var row_number = aData[1];			
		// // 	var regional_name = aData[2];
		// // 	var division_lob = aData[3];
		// // 	var created_by = aData[4];
		// // 	var updated_by = aData[5];
		// // 	var created_at = aData[6];
		// // 	var updated_at = aData[7];
		// // 	var action = aData[8];
		// // 	var row_id = aData[9];
		// },
		'rowId': 'col_row_id',
        columnDefs: [ 
            // {
            //     orderable: false,
            //     className: 'select-checkbox',
            //     targets:   0
            // },
            // {visible:false,targets:18},
            {"className": "dt-center", "targets": "_all"},
        ],
        // select: {
        //     style:    'os',
        //     selector: 'td:first-child'
        // }
	})

	// Create New Region
	$("#bTnSaveRegional").on("click", function() {

		error = true
		if ($("#regional_name").val() == '')
		{
			$("#error_regional_name").text("Missing regional name")
			error = false
		}
		if ($("#division_lob").val() == '')
		{
			$("#error_division_lob").text("Missing division/lob name")
			error = false
		}

		if (error == true) {

			$.ajax({
				url: "db/regional.php",
				dataType: "json",
				type: "post",
				data: $("#formRegional").serialize() + "&function=create",
				success: function(response) {

					console.log(response)
					if (response.data == 'failed') {
						$('#error_modal').modal("show");
						$('#error_msg').text(response.message)
					} else {
						$('#success_modal').modal("show");
						$('#regional_modal').modal("hide");
					}

					table_master_list_regional_table.ajax.reload();

				}
			})

		}

	})

	$("#bTnEditRegional").on("click", function() {

		error = true
		if ($("#regional_name").val() == '')
		{
			$("#error_regional_name").text("Missing regional name")
			error = false
		}
		if ($("#division_lob").val() == '')
		{
			$("#error_division_lob").text("Missing division/lob name")
			error = false
		}

		if (error == true) {

			$.ajax({
				url: "db/regional.php",
				dataType: "json",
				type: "POST",
				data: $("#formRegional").serialize() + "&function=edit&rowid="+table_master_list_regional_table.row('.selected').id(),
				success: function(response) {

					// console.log(response)
					// return false;
					if (response.data == 'failed') {
						$('#error_modal').modal("show");
						$('#error_msg').text(response.message)
					} else {
						$('#success_msg').text(response.message)
						$('#success_modal').modal("show");
						$('#regional_modal').modal("hide");
					}

					table_master_list_regional_table.ajax.reload();

				}
			})

		}

	})








	// Edit Region
    $("#master_list_regional_table tbody").on( 'click', 'button[name=editFunction]', function () {

    
    	// console.log(table_master_list_regional_table.row('.selected').id())
    	var selected_row_data = table_master_list_regional_table.row('.selected').data();
		console.log(selected_row_data);

        // if (clean_pipe_iptransit.row('.selected').id() === undefined) {
        //     $("#failed").modal("show");
        //     $("#error_msg_header").text("Error!");
        //     $("#error_msg").html('No circuit is selected, click on any circuit.')
        // } else {

        // }

    });


});

	
