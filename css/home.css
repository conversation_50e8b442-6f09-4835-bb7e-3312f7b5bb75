@import url("font.css");
@import url("custom_datatable.css");

body{
	min-height: 100vh; 
}

.flex-grow {
   flex: 1;
}
.logo{
	font-family: 'neo-san-bold';
	font-style: italic;
	font-size: 30px;
	color: #ee7202 !important;
	letter-spacing: -1px;
	padding: 0;
	font-weight: normal;
	line-height: 30px
}

.red{background: red}

/* SHIFT */
.shift ul li a {
  position:relative;
  z-index: 1;
  text-align: center !important
}
.shift ul li a:hover {
  color: white !important;
}
.shift ul li a:after {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  width: 100%;
  height: 1px;
  content: '.';
  color: transparent;
  /*background: #ee7202;*/
  background: #143e8c;
  visibility: none;
  opacity: 0;
  z-index: -1;
}
.shift ul li a:hover:after {
  opacity: 1;
  visibility: visible;
  height: 100%;
}

.shift ul li a,
.shift ul li a:after,
.shift li a:before {
  transition: all .5s;
}

.custom_navbar{
	padding: 0 10px;
	border-bottom: 1px solid #cccccc;
	box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
	/*background-color: #143e8c*/
}



.nav-link,
.navbar-text{
	color: #222222 !important;
	font-size: 12px;
}
.bg-orange{
  background: #143e8c;
  color: #eeeeee !important
}
.navbar-nav .nav-item:first-child{
	border-left: 1px solid #ddd
}

.navbar-nav .nav-item{
	border-right: 1px solid #ddd
}

.nav-link > .active > a {
	background: #cccccc;  
}



.copyright{
  text-align: center; 
  font-size: 12px; 
  color: #eeeeee;
  padding-top: 10px;
  line-height: 15px;
  font-weight: normal;
}

.header{
	height: 50px;
	padding-top: 15px;
	text-align: center;
}
.header h4{
	color: #222222;
	font-size: 17px !important;
}
.content2_header{
	background-color: #eeeeee;
	font-size: 12px;
	padding:4px;
}
.content2{
	background-color: #eeeeee;
	font-size: 12px;
	padding: 20px
}
.content2 select{
	border: 1px solid #cccccc;
	/*padding: 3px;*/
	box-shadow: none;
}
.search_section button{margin: 6px !important;}
.content2 p{margin-right: 5px;margin-left: 5px}
.content2 p:first-child{margin-left: 0}

.dataTables_paginate,
.dataTables_info{
	margin-top: 10px !important
} 

.content2 label{
	margin-bottom: 10px !important
}

.search_section label{margin-right: 6px;} 

.modal-table{padding: 5px 10px; background: transparent}

.menu a{
	border: 1px solid #cccccc;
	border-bottom: none
}

.menu a:last-child{border-bottom: 1px solid #cccccc;}

.col-form{padding-left: 0}
.form{
	background-color: #eeeeee;
	font-size: 12px !important;
	padding: 20px;
	height: 100%
}
.form input,
.content2_header select,
.modal-form input,
.modal-form3 input,
.modal-form3 select,
.form textarea,
.modal-form textarea,
.modal-form3 textarea,
.feedback textarea,
.modal-form2 textarea{border-radius: 0}
.form input:hover,
.modal-form input:hover,
.modal-form3 input:hover,
.modal-form3 select:hover,
.form textarea:hover,
.content2_header select:hover,
.modal-form2 textarea:hover,
.modal-form3 textarea:hover,
.modal-form textarea:hover,
.modal-form input:focus,
.content2_header select:focus,
.modal-form3 input:focus,
.modal-form3 select:focus,
.modal-form3 textarea:focus,
.form input:focus,
.form textarea:focus,
.modal-form2 textarea:focus,
.feedback textarea:hover,
.feedback textarea:focus{border:1px solid #999999}
.col-form-label-sm,
.form-control-sm{font-size: 12px}
.content2 h6,
.form h6{margin-bottom: 20px;padding-bottom: 10px;text-align: center;border-bottom: 1px solid #ccc;font-size: 14px}
.content2 h6{text-align: left}
.form button,
.content2 button,
.content2_header button{
	border-radius: 0;
	background: #143e8c;
	color: #eeeeee;
	font-size: 12px
}
.content2 button{margin-bottom: 10px;margin-left: 3px}

.menu_btn{
	color: #eeeeee !important;
	background-color: #143e8c;
    box-shadow: 0 0 1px #ccc;
    -webkit-transition-duration: 0.5s;
    -webkit-transition-timing-function: linear;
    box-shadow:0px 0 0 #ee7202  inset;
}
.menu_btn:hover{
    box-shadow: 5px 0 0 #ee7202 inset;
}
.active_btn{box-shadow: 5px 0 0 #ee7202 inset}
.modal-header{padding: 10px 15px;justify-content: center}
.modal-title{font-size: 15px}
.single_form button,
.modal-footer button,
.test button,
.verify_content button{
	border-radius: 0;
	background: #143e8c;
	color: #eeeeee;
	font-size: 12px;
}
.single_form button{margin: 10px auto}
.modal-footer {border-top: 0;padding: 10px 15px}
.test {border-top: 0;padding: 10px 15px}
.modal_content label{margin-bottom: 2px !important}
.modal_content .form-group{margin-bottom: 2px}
.modal-body{padding: 10px 0px 20px 0px}
.modal-form:nth-of-type(1){padding-right: 5px;padding-left:20px}
.modal-form:nth-of-type(2){padding-left: 5px;padding-right:20px}
.modal-form2{padding-left: 20px;padding-right:20px}
.modal-content{border-radius: 0}

/*Change fade style*/
.modal-dialog{
  margin:10px auto;
}

.fade2 {
    transform: scale(0.9);
    opacity: 0;
    transition: all .2s linear;
}

.fade2.show {
    opacity: 1;
    transform: scale(1);
}

#success_modal .modal-body,
#error_modal .modal-body{
	background-color: #00cc66;
	padding: 15px;
	color: #eeeeee !important;
	display: flex;
	justify-content: center;
}
#error_modal .modal-body{background-color: #ff4d4d}
#success_modal .modal-body img,
#error_modal .modal-body img{height: 40px;width: 40px}
#success_modal .modal-body h4,
#error_modal .modal-body h4{margin: 0;font-size: 17px}
#success_modal .modal-body button,
#error_modal .modal-body button{padding: 0;border: 0;background: transparent; color: #eeeeee;height: 20px !important}
#success_modal .modal-body button span,
#error_modal .modal-body button span{font-size: 24px;margin: 0;line-height: 1px}
#success_modal .modal-body .success_msg{font-size: 12px;}
#error_modal .modal-body .success_msg{font-size: 12px;}
#success_modal .modal-body .msg,
#error_modal .modal-body .msg{vertical-align: middle;padding:0px 10px}

.single_form{padding: 0 20px}

@media (max-width: 767px) {
	.navbar-nav .nav-item:first-child{border: 1px solid #ddd}
	.navbar-nav .nav-item{border: 1px solid #ddd;border-top: none;}
	.last-ref{width: 100% !important;border-top: none !important;margin-bottom: 5px !important}
	.menu{margin-bottom: 10px}
	.form{margin-left: 7px}
	.content2{padding: 10px 5px;}
	.modal-form:nth-of-type(1),
	.modal-form:nth-of-type(2){padding-left: 20px;padding-right:20px}
	.content2_header button{margin: 5px auto 5px 10px;float: left}
}


/*User Profile*/
.profile_content{
	box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
	max-width: 800px;
	margin-bottom: 20px;
}
.profile_content.feedback{
	padding: 20px;
}
.feedback button,
.feedback button:hover,
.feedback button:focus{
	border-radius: 0;
	background: #143e8c;
	color: #eeeeee
}
.feedback textarea{margin-bottom: 10px;font-size: 0.9rem}
.img_section{
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center
}
.profile-img{
	padding: 20px;
}
.profile-img img{
    border: 1px solid #ddd;
    padding: 5px;
}
.profile-img button{
	border-radius: 0;
	background: #143e8c;
	color: #eeeeee;
	font-size: 12px
}
.profile-img h1{font-size: 20px;margin-top: 15px;margin-bottom: 25px}
.profile-info{
	padding: 15px
}
.profile-info input{
	border-radius: 0;
	border: 0;
	padding: 0;
}
.editable{
	border: 1px solid #ddd !important;
	padding: 5px !important;
	max-width: 300px
}
.profile-info label{
	padding: 0;
	line-height: 1.5;
	font-weight: bold
}
.profile-info button{
	background: transparent;
	border: 0;
}
.profile_content .form-control[readonly] {background-color: white !important}
.badge_custom{background: #ee7202}
.verify_content p{font-size: 13px;text-align: center}
.verify_content buttton {text-align: center}

.form-text{font-size: 12px}
.text-danger{color: red;}

.modal-form3 input[type="file"],
.modal-form3 input[type="file"]:hover,
.modal-form3 input[type="file"]:focus{
	border: 0px;
	padding: 0;
}

.table_custom{
	font-size: 11px !important;
}

.popover-body {
height: 400px;
overflow-y: scroll;
}

.upload-box{
	max-width: 300px;
	background-color: white;
	padding: 20px;
	margin:1px auto 10px auto;
}

/*input type file*/
div.browse-wrap {
    top:0;
    left:0;
    cursor:pointer;
    overflow:hidden;
    padding:10px;
    text-align:center;
    position:relative;
    background-color:#f6f7f8;
    border:solid 1px #d2d2d7;
}
div.title {
    color:#3b5998;
    font-size:14px;
    font-weight:bold;
}
input.upload {
    right:0;
    margin:0;
    bottom:0;
    padding:0;
    opacity:0;
    height:300px;
    outline:none;
    cursor:inherit;
    position:absolute;
    font-size:1000px !important;
}
span.upload-path {
    text-align: center;
    margin:10px 0px;
    display:block;
    font-size: 80%;
    color:#3b5998;
    font-weight: normal;
}

.navbar-toggler{
	border-radius: 0;
	border: 1px solid #eee}
}

.notify_red{
	background-color: red;
}

/* Chrome, Safari, Edge, Opera */
/* input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
} */


