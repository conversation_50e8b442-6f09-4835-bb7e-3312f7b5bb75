@import url("font.css");

body{font-family: 'opensan-reg';}

/*Center modal*/
.modal_login {
  text-align: center;
  padding: 0!important;
}

.modal_login:before {
  content: '';
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  margin-right: -4px;
}

#login .modal-dialog {
  display: inline-block;
  text-align: left;
  vertical-align: middle;
}
/*Change fade style*/
.fade-scale {
  transform: scale(1);
  opacity: 0;
  -webkit-transition: all .25s linear;
  -o-transition: all .25s linear;
  transition: all .25s linear;
}

.fade-scale.in {
  opacity: 1;
  transform: scale(1);
}
/*Remove dark background modal*/
.modal-backdrop {
   background-color: transparent;
}
/*Customize modal*/
.modal-content{
	border-radius: 0;
	/*border: 2px solid #ddd !important;*/
	border: 1px solid white !important;
	-webkit-box-shadow: none !important;
          box-shadow: none !important;
}

.modal-title{
	font-size: 25px;
	padding: 0;
	font-family: 'opensan-light';
	line-height: 30px;
	font-weight: normal;
	margin-left: 45px;
}

.modal-header{
	/*background-color: #ee7202;*/
	padding: 5px;
	border-bottom: 1px solid white !important;
	/*border-top-left-radius: 20px;
	border-top-right-radius: 20px;*/
}

.modal-body{
	padding: 30px 50px 30px 50px
}

.modal-body input{
	border: none;
	border-radius: 100px;
	/*background: #ddd;*/
	border: 2px solid #ddd;
	box-shadow: none !important;
	text-align: left;
	font-size: 12px;
	padding-left: 40px
}

.modal-body input:hover,
.modal-body input:focus{border: 2px solid #aaa}

.mail {
	background: url(../images/id.png) no-repeat left 15px center;
}

.lock {
	background: url(../images/lock.png) no-repeat left 15px center;
}

.modal-body p{
	font-size: 12px;
	margin: 20px auto 15px 15px;
}

#login .modal-body button{
	display: inline-block;
	border-radius: 100px;
	background-color: #143e8c;
	border: none;
	color: #fff;
	text-align: center;
	font-size: 12px;
	padding: 8px;
	padding-right: 2px;
	transition: all 0.5s;
	cursor: pointer;
	font-family: 'opensan-bold';
	text-transform: uppercase;
}

.modal-footer{padding: 0; border-top:1px solid white; text-align: center}
.copyright{margin: 0;width: 250px;margin-left: 45px;font-size: 11px;line-height: 15px}
.copyright img{width: 12px;height: 12px}

.button{
    border:	0;
    color:	#fff;
    box-shadow: 0 0 1px #ccc;
    -webkit-transition-duration: 0.5s;
    -webkit-transition-timing-function: linear;
    box-shadow:0px 0 0 #194fb3  inset;
}
.button:hover{
    -webkit-box-shadow: -300px 0 0 #194fb3 inset;
}

.comme_logo{
	font-family: 'neo-san-bold';
	font-style: italic;
	font-size: 60px;
	color: #ee7202;
	letter-spacing: -3px;
}

/*Checkbox*/
/* Base for label styling */
[type="checkbox"]:not(:checked),
[type="checkbox"]:checked {
  position: absolute;
  left: -9999px;
}
[type="checkbox"]:not(:checked) + label,
[type="checkbox"]:checked + label {
  position: relative;
  padding-left: 1.95em;
  cursor: pointer;
}

/* checkbox aspect */
[type="checkbox"]:not(:checked) + label:before,
[type="checkbox"]:checked + label:before {
  content: '';
  position: absolute;
  left: 0; top: 0;
  width: 1.25em; height: 1.25em;
  border: 2px solid #ddd;
  background: #fff;
  border-radius: 4px;
  box-shadow: inset 0 1px 3px rgba(0,0,0,.1);
}
/* checked mark aspect */
[type="checkbox"]:not(:checked) + label:after,
[type="checkbox"]:checked + label:after {
  content: '✔';
  position: absolute;
  top: .1em; left: .3em;
  font-size: 13px;
  line-height: 0.8;
  color: #ee7202;
  transition: all .2s;
  font-weight: bold;
}
/* checked mark aspect changes */
[type="checkbox"]:not(:checked) + label:after {
  opacity: 0;
  transform: scale(0);
}
[type="checkbox"]:checked + label:after {
  opacity: 1;
  transform: scale(1);
}
/* disabled checkbox */
[type="checkbox"]:disabled:not(:checked) + label:before,
[type="checkbox"]:disabled:checked + label:before {
  box-shadow: none;
  border-color: #bbb;
  background-color: #ddd;
}
[type="checkbox"]:disabled:checked + label:after {
  color: #999;
}
[type="checkbox"]:disabled + label {
  color: #aaa;
}

/* hover style just for information */
label:hover:before {
  border: 2px solid #aaa!important;
}

.modal-body label{font-weight: normal;font-family: opensan-reg;color: #555}

/*#error_msg .modal-body{padding: 10px;background: #ff6961; color: white;text-align: center;border-bottom: 3px solid #ff3d33}

#error_msg .modal-dialog{margin-top: 0}

.btn_retry,
.btn_retry:hover,
.btn_retry:active
{color: #fff;padding: 7px 15px;display:inline-block;text-transform: uppercase;border-radius:100px;background:transparent;border:2px solid white;font-family: opensan-bold; font-size: 11px;margin-left:20px }
*/


#error_msg .modal-body{
	background-color: #00cc66;
	padding: 15px;
	color: #eeeeee !important;
	display: flex;
	justify-content: center;
}
#error_msg .modal-body{background-color: #ff4d4d}
#error_msg .modal-body img{height: 40px;width: 40px}
#error_msg .modal-body h4{margin: 0;font-size: 20px}
#error_msg .modal-body button{padding: 0;border: 0;background: transparent; color: #eeeeee;height: 20px !important}
#error_msg .modal-body button span{font-size: 24px;margin: 0;line-height: 1px}
#error_msg .modal-body .success_msg{font-size: 12px;}
#error_msg .modal-body .msg{vertical-align: middle;padding:0px 30px 0px 20px}

.modal-title img{
	height: 46px;
	width: 250px;
}

@media (max-width: 767px) {
	.modal-title{margin-left: 12px;}
	.copyright{margin-left: 10px;font-size: 11px}
	.comme_logo{font-size: 55px}
	.modal-body{padding-left: 20px;padding-right: 20px;}
}

.navbar-toggler{border: 0px;}
