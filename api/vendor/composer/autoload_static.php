<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit6ee670ca1b3f1ee91ea2859538212a27
{
    public static $prefixLengthsPsr4 = array (
        'F' => 
        array (
            'Firebase\\JWT\\' => 13,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Firebase\\JWT\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
            1 => __DIR__ . '/..' . '/firebase/php-jwt/src',
        ),
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit6ee670ca1b3f1ee91ea2859538212a27::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit6ee670ca1b3f1ee91ea2859538212a27::$prefixDirsPsr4;

        }, null, ClassLoader::class);
    }
}
