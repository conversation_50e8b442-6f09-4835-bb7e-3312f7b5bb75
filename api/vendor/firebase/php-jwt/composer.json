{"name": "firebase/php-jwt", "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "license": "BSD-3-<PERSON><PERSON>", "require": {"php": ">=5.3.0"}, "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "require-dev": {"phpunit/phpunit": " 4.8.35"}}