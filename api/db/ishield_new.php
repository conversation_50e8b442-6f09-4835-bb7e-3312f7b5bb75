<?php

header("Access-Control-Allow-Origin: *");
require_once 'headers.php';

class ishield_login {	
	
	public function check($username, $password, $find) { 
		if($find != null){
			$ldap = $this->check_login($find,$username,$password);
			return $ldap;
		} else { 
			$find = $username; 
			$ldap = $this->check_login($find,$username,$password);
			return $ldap;
		}
	}
	
	private function check_login($find,$username,$password) {
		
		//$adServer = "ldaps://10.54.5.231";		//lab
		$adServer = "ldaps://10.45.236.28";			//production
		$ldap = ldap_connect($adServer,636);

		ldap_set_option($ldap, LDAP_OPT_PROTOCOL_VERSION, 3);
		ldap_set_option($ldap, LDAP_OPT_REFERRALS, 0);
		
		$ldap_user   = "cn=ecommeldapadmin, ou=serviceAccount, o=Telekom";
		//$ldap_pass   = "4XzXbTMa" ;
		$ldap_pass   = "A1UeCxM";

		if($ldapbind = @ldap_bind($ldap, $ldap_user, $ldap_pass))
		{
			//echo "Bind success \n";
			if($find!=$username){
				$result = ldap_search($ldap,'ou=users, o=data','cn='.$find);
				$data = ldap_get_entries($ldap, $result);
				
				if ($data[0]["cn"][0] != ""){
					$uid = $data[0]["cn"][0];
					$ic = $data[0]["ppnewic"][0];
					$name = $data[0]["fullname"][0];
					$mobile = $data[0]["mobile"][0];
					$unit = $data[0]["pporgunitdesc"][0];
					$position = $data[0]["pppostdesc"][0];
					$report = $data[0]["ppreporttoname"][0];
					$mail = $data[0]["mail"][0];
					
					//echo json_encode(array($data));
					// echo $post_data = json_encode(array('status' => 'succeed','staffid' => $uid,'mail' => $mail,'staffname' => $name,'mobile' => $mobile,'unit' => $unit, 'position' => $position,'report' => $report));	
					return array('status' => 'succeed','staffid' => $uid,'mail' => $mail,'staffname' => $name,'mobile' => $mobile,'unit' => $unit, 'position' => $position,'report' => $report);
				}else {
					return array('status' => 'invalid');
				}
				} else {
				$resource = @ldap_bind($ldap, 'cn='.$username.', ou=users, o=data', $password);
				if($resource){
					$result = ldap_search($ldap,'ou=users, o=data','cn='.$username);
					$data = ldap_get_entries($ldap, $result);
					
					$uid = $data[0]["cn"][0];
					$ic = $data[0]["ppnewic"][0];
					$name = $data[0]["fullname"][0];
					$mobile = $data[0]["mobile"][0];
					$unit = $data[0]["pporgunitdesc"][0];
					$position = $data[0]["pppostdesc"][0];
					$report = $data[0]["ppreporttoname"][0];
					$mail = $data[0]["mail"][0];
					
					//echo json_encode(array($data));
					return (array('status' => 'succeed','staffid' => $uid,'mail' => $mail,'staffname' => $name,'mobile' => $mobile,'unit' => $unit, 'position' => $position,'report' => $report));	
				} else {
					//echo json_encode(array($resource));
					return array('status' => 'invalid');
				}
			}
		} else{return array('status' => 'invalid');}
		
	}
	
	/*
	private function ldap_escapes($subject, $dn = FALSE, $ignore = NULL)
	{
			// The base array of characters to escape
			// Flip to keys for easy use of unset()
			$search = array_flip($dn ? array('\\', ',', '=', '+', '<', '>', ';', '"', '#') : array('\\', '*', '(', ')', "\x00"));

			// Process characters to ignore
			if (is_array($ignore)) {
					$ignore = array_values($ignore);
			}
			for ($char = 0; isset($ignore[$char]); $char++) {
					unset($search[$ignore[$char]]);
			}

			// Flip $search back to values and build $replace array
			$search = array_keys($search); 
			$replace = array();
			foreach ($search as $char) {
					$replace[] = sprintf('\\%02x', ord($char));
			}

			// Do the main replacement
			$result = str_replace($search, $replace, $subject);

			// Encode leading/trailing spaces in DN values
			if ($dn) {
					if ($result[0] == ' ') {
							$result = '\\20'.substr($result, 1);
					}
					if ($result[strlen($result) - 1] == ' ') {
							$result = substr($result, 0, -1).'\\20';
					}
			}

			return $result;
	}*/
}
	
	
	
	/*
	$postdata = file_get_contents("php://input");
  	
	
	if ($postdata) {
		
        $request = json_decode($postdata);
		$username = $request->username;
		$password = $request->password;
		//$msg = $auth->authUser($username, $password);
		
		check_login($username,$password);
		
		//echo json_encode(array($msg));
		
    } else {
        echo json_encode(array("status"=>"Not called properly"));
    }*/
 ?>