<?php
	require_once('vendor/autoload.php');
	use \Firebase\JWT\JWT; 
	require_once('db/ez_sql_core.php');
	require_once('db/ez_sql_sqlsrv.php');
	require_once('ishield_new.php');
	//require_once('ishield_login.php');
	
	/*
	define('DBHost', '**************'); 
	define('DBName', 'COMME');
	define('DBUser', 'comme'); 
	define('DBPassword', '%user456'); */
	
	// create a class for easy code structure and use
	class userAuth {
		
		// create an empty id variable to hold the user id
		private $email, $staffid, $fullname, $unit, $mobile, $position, $report, $conn, $stmt, $db, $userpassword, $workgroup;
		 
		// key for JWT signing and validation, shouldn't be changed
		private $key = "ecomme_famiera_aman";
		
		// return signed token to user if the user exists
		public function authUser($username, $password) { 
		  // check if the user exists
		  if ($this->validUser($username, $password)) {
			// generate JSON web token and store as variable
			$token = $this->genJWT();
			return $token;
			
		  } else {
			//return header('HTTP/1.1 401 Unauthorized', true, 401);//http_response_code(401);
			return array("status"=>"failed");
		  }
		}
		
		// Checks if the user exists in the database
		private function validUser($username, $password) {
			
			$ishield_login = new ishield_login();
			$data = $ishield_login->check($username,$password,null);
			$status = $data["status"];
			
			// doing a user exists check with minimal to no validation on user input
			if ($status=="succeed") {
				$staffid = $data["staffid"];
				$mail = $data["mail"];
				$staffname = $data["staffname"];
				$unit = $data["unit"];
				$mobile = $data["mobile"];
				$position = $data["position"];
				$report = $data["report"];
	
				$db = new ezSQL_sqlsrv(DBUser, DBPassword, DBName, DBHost);
				$n = $db->get_var("SELECT count(*) as idCount FROM USERS WHERE staff_id='$staffid'");
				
				// update db if user not in db
				if($n<1){
					$db->query("INSERT INTO USERS (staff_id,fullname,email,department,contact_number,designation,supervisor_name,workgroup) VALUES ('$staffid','$staffname','$mail','$unit','$mobile','$position','$report',null)");
				}
				else{
					$db->query("UPDATE [USERS] SET designation='$position', department='$unit', supervisor_name='$report' WHERE staff_id='$staffid'");		
				}
				$data = $db->get_results("SELECT * FROM USERS WHERE staff_id='$staffid'");
				//print_r($data);
				$this->staffid = $data[0]->staff_id;
				$this->staffid = $staff_id;
				$this->fullname = $data[0]->fullname;
				$this->fullname = $staffname;
				$this->email = $data[0]->email;
				$this->unit = $data[0]->department;
				$this->mobile = $data[0]->contact_number;
				$this->position = $data[0]->designation;
				$this->report = $data[0]->supervisor_name;
				$this->workgroup = $data[0]->workgroup;
				$this->userpassword = $password;
				
				// Print out last query and results..
				//$db->debug();
				return true;
			} else {
				return false;
			}
		}
		
		// Generates and signs a JWT for User
		private function genJWT() {
		  // Make an array for the JWT Payload
		  $payload = array(
			//"userpassword" => $this->userpassword,
			"staffid" => $this->staffid,
			"fullname" => $this->fullname,
			"email" => $this->email,
			"mobile" => $this->mobile,
			"unit" => $this->unit,
			"workgroup" => $this->workgroup,
			"exp" => time() + (60 * 60)
		  );
		  
		  //print_r($payload);
		 
		  // encode the payload using our secretkey and return the token
		  return array("status"=>"succeed","token"=>JWT::encode($payload, $this->key),"fullname"=>$this->fullname);
		}
		
		// Validates a given JWT from the user email
		private function validJWT($token) {
			$res = array(false, '');
			// using a try and catch to verify
			try {
			  $decoded = JWT::decode($token, $this->key, array('HS256'));
			} catch (Exception $e) {
			  return $res;
			}
			$res['0'] = true;
			$res['1'] = (array) $decoded;

			return $res;
		}
		
		// Validates a given token
		public function validtoken($token,$type) {
			// check if token is valid
			$tokenVal = $this->validJWT($token);
			if ($tokenVal['0']) { 
				if ($type == "userdetails") {
					unset($tokenVal['1']['userpassword']); //remove userpassword from view
					return $tokenVal['1'];
				} else {return true;}
			} 
			else { return false; }
		}
		
		// Validates a given token
		public function finduser($token,$find) {
			// check if token is valid
			$tokenVal = $this->validJWT($token);
			if ($tokenVal['0']) { 
				//check with ldap if user not in database
				$username = $tokenVal['1']['staffid'];
				$password = $tokenVal['1']['userpassword'];
				
				
				$ishield_login = new ishield_login();
				$data = $ishield_login->check($username,$password,$find);
				//$status = $data["status"];
				
				//famiera
				$status = $data["status"];
				// doing a user exists check with minimal to no validation on user input
				if ($status=="succeed") {
					$staffid = $data["staffid"];
					$mail = $data["mail"];
					$staffname = $data["staffname"];
					$unit = $data["unit"];
					$mobile = $data["mobile"];
					$position = $data["position"];
					$report = $data["report"];
					$report = filter_var($report,FILTER_SANITIZE_SPECIAL_CHARS);
		
					$db = new ezSQL_sqlsrv(DBUser, DBPassword, DBName, DBHost);
					$n = $db->get_var("SELECT count(*) as idCount FROM USERS WHERE staff_id='$staffid'");
					//echo $n;
					// update db if user not in db
					if($n<1){
						echo "INSERT INTO USERS (staff_id,fullname,email,department,contact_number,designation,supervisor_name,workgroup) VALUES ('$staffid','$staffname','$mail','$unit','$mobile','$position','$report',null)";
						$db->query("INSERT INTO USERS (staff_id,fullname,email,department,contact_number,designation,supervisor_name,workgroup) VALUES ('$staffid','$staffname','$mail','$unit','$mobile','$position','$report',null)");
					}
					else{
						$db->query("UPDATE [USERS] SET designation='$position',email='$mail', department='$unit', supervisor_name='$report' WHERE staff_id='$staffid'");		
					}					
					
				} else {
					return false;
				}
				return $data;
				
			} 
			else { return false; }
		}
	}
	
?>