<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit31af1806cbebb667d13af919fc992a51
{
    public static $prefixLengthsPsr4 = array (
        'F' => 
        array (
            'Firebase\\JWT\\' => 13,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Firebase\\JWT\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit31af1806cbebb667d13af919fc992a51::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit31af1806cbebb667d13af919fc992a51::$prefixDirsPsr4;

        }, null, ClassLoader::class);
    }
}
