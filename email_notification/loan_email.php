<?php
		error_reporting(0);
		require_once 'swift_required.php';
		
			 $owneremail         = filter_var($_GET['owneremail']);										
			 $asset_no           = filter_var($_GET['asset_no']);										
			 $inventory_name     = filter_var($_GET['inventory_name']);										
			 $borrower_email     = filter_var($_GET['borrower_email']);
			 $borrower_name      = filter_var($_GET['borrower_name']);
			 $loan_date          = filter_var($_GET['loan_date']);
			 $purpose_borrowing  = filter_var($_GET['purpose_borrowing']);
			 $verifiedby         = filter_var($_GET['verifiedby']);
			
			//10.48.3.100
			//10.48.8.100
			//webmail2014.tm.com.my

			//Create the Transport
			// $transport = Swift_SmtpTransport::newInstance('10.48.3.100', 25);
			//$transport = Swift_SmtpTransport::newInstance('smtp2.tm.com.my', 25);
			$transport = Swift_SmtpTransport::newInstance('172.24.31.7 25', 25);

			//Create the Mailer using your created Transport
			$mailer = Swift_Mailer::newInstance($transport);
			

			//Create a message
			$message = Swift_Message::newInstance('<just bg nama instance>')
			->setSubject('Test Gear Movement Tracking : '.$asset_no.' - '.$inventory_name )
			->setFrom(array('<EMAIL>' => 'ecomme'))
			->setTo(array($borrower_email))
			->setCc(array($owneremail))
			->setBody(
			'<html>' .
			' <head></head>' .
			' <body>' .
			' Dear '.$borrower_name.',<br><br>
			Test Gear below has been assigned to your supervision. </br></br>
			Test Gear : '.$asset_no.' - '.$inventory_name.'</br>
			Loan Date : '.$loan_date.'</br>
			Purpose borrowing : '.$purpose_borrowing.'</br>
			Test Gear Owner : '.$verifiedby.'</br></br>
			eCOMME : https://ifme.tm.com.my/eCOMME</br>
            Kindly log on to COMME website for further details.'.
			'<br><br>Thank you' .
			'</body>' .
			'</html>',
			'text/html' // Mark the content-type as HTML
			)
			;
			//Send the message
			$result = $mailer->send($message); 
			/* if(!$mailer->send()) {
				echo "Mailer Error: " . $mail->ErrorInfo;
			} 
			else {
				echo "Message has been sent successfully";
			} */
			/* if (!$mailer->send($message, $errors))
				{
					echo "Error:";
					print_r($errors);
				} */
			
			echo "SUCCESS";

?>